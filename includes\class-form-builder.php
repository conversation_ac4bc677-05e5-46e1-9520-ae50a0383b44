<?php
/**
 * Form Builder Class
 *
 * Handles all form-related functionality including:
 * - Form creation and management
 * - Form rendering
 * - Form submission processing
 * - Form validation
 *
 * @package    Database_App_Builder
 * @subpackage Database_App_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Form_Builder {

    /**
     * Initialize the class
     */
    public function __construct() {
        // Register shortcode
        add_shortcode('dab_form', array($this, 'render_form_shortcode'));

        // Register form submission handler
        add_action('init', array($this, 'process_form_submission'));

        // Register AJAX handlers
        add_action('wp_ajax_dab_submit_form', array($this, 'ajax_process_form_submission'));
        add_action('wp_ajax_nopriv_dab_submit_form', array($this, 'ajax_process_form_submission'));
    }

    /**
     * Get form by ID
     *
     * @param int $form_id Form ID
     * @return object|false Form object or false if not found
     */
    public function get_form($form_id) {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';

        $form = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $forms_table WHERE id = %d", $form_id)
        );

        if ($form) {
            // Unserialize fields if needed
            if (!empty($form->fields)) {
                $form->fields = maybe_unserialize($form->fields);
            }

            // Decode conditional logic if needed
            if (!empty($form->conditional_logic)) {
                $form->conditional_logic_rules = json_decode($form->conditional_logic, true);
            }
        }

        return $form;
    }

    /**
     * Get fields for a form
     *
     * @param int $form_id Form ID
     * @return array Array of field objects
     */
    public function get_form_fields($form_id) {
        global $wpdb;
        $forms_table = $wpdb->prefix . 'dab_forms';
        $fields_table = $wpdb->prefix . 'dab_fields';

        // Get form data
        $form = $this->get_form($form_id);
        if (!$form) {
            return array();
        }

        // Get all fields for the table
        // Check if field_order column exists
        $field_order_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM information_schema.columns
                WHERE table_schema = %s
                AND table_name = %s
                AND column_name = 'field_order'",
                DB_NAME,
                $fields_table
            )
        );

        if ($field_order_exists) {
            $fields = $wpdb->get_results(
                $wpdb->prepare("SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order ASC", $form->table_id)
            );
        } else {
            // Fallback to ordering by ID if field_order doesn't exist
            $fields = $wpdb->get_results(
                $wpdb->prepare("SELECT * FROM $fields_table WHERE table_id = %d ORDER BY id ASC", $form->table_id)
            );
        }

        if (empty($fields)) {
            // No fields exist for this table
            return array();
        }

        // Get selected fields for this form
        $selected_fields = $form->fields;

        // If no fields are selected or the fields property is not an array, return all fields
        if (empty($selected_fields) || !is_array($selected_fields)) {
            // Auto-fix: Update the form to include all fields
            $field_slugs = array();
            foreach ($fields as $field) {
                $field_slugs[] = $field->field_slug;
            }

            // Update the form with all fields
            $wpdb->update(
                $forms_table,
                array('fields' => maybe_serialize($field_slugs)),
                array('id' => $form_id)
            );

            return $fields;
        }

        // Filter fields if specific ones are selected
        $filtered_fields = array();
        foreach ($fields as $field) {
            if (in_array($field->field_slug, $selected_fields)) {
                $filtered_fields[] = $field;
            }
        }

        // If no fields match after filtering, return all fields
        if (empty($filtered_fields)) {
            return $fields;
        }

        return $filtered_fields;
    }

    /**
     * Render form shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Form HTML
     */
    public function render_form_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => 0,
        ), $atts, 'dab_form');

        if (empty($atts['id'])) {
            return '<p class="dab-error">' . __('Error: Form ID is required', 'db-app-builder') . '</p>';
        }

        $form_id = intval($atts['id']);

        // Get form data
        $form = $this->get_form($form_id);
        if (!$form) {
            return '<p class="dab-error">' . __('Error: Form not found', 'db-app-builder') . '</p>';
        }

        // Get form fields
        $fields = $this->get_form_fields($form_id);

        // If no fields found, try to create default fields for the table
        if (empty($fields)) {
            // Get table information
            $table_id = $form->table_id;
            $tables_table = $wpdb->prefix . 'dab_tables';
            $table_info = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $tables_table WHERE id = %d",
                $table_id
            ));

            if ($table_info) {
                // Create default fields for this table
                $created_fields = DAB_DB_Manager::create_default_fields($table_id, $table_info->table_slug);

                if (!empty($created_fields)) {
                    // Get the field slugs
                    $fields_table = $wpdb->prefix . 'dab_fields';
                    $field_slugs = $wpdb->get_col($wpdb->prepare(
                        "SELECT field_slug FROM $fields_table WHERE id IN (" . implode(',', array_map('intval', $created_fields)) . ")"
                    ));

                    // Update the form to use these fields
                    if (!empty($field_slugs)) {
                        $wpdb->update(
                            $wpdb->prefix . 'dab_forms',
                            array('fields' => maybe_serialize($field_slugs)),
                            array('id' => $form_id)
                        );

                        // Refresh fields
                        $fields = $this->get_form_fields($form_id);
                    }
                }
            }
        }



        if (empty($fields)) {
            return '<p class="dab-error">' . __('Error: No fields found for this form. Please create fields for the table first.', 'db-app-builder') . '</p>';
        }

        // Check if form was successfully submitted
        $form_success = isset($_GET['dab_form_success']) && isset($_GET['form_id']) && intval($_GET['form_id']) === $form_id;

        // Enqueue required scripts and styles
        $this->enqueue_form_assets($form);

        // Start output buffering
        ob_start();

        // Show success message if form was submitted
        if ($form_success) {
            echo '<div class="dab-form-success" style="background-color: #d4edda; color: #155724; padding: 15px; margin-bottom: 20px; border-radius: 4px; border-left: 4px solid #28a745;">';
            echo '<strong>' . __('Success!', 'db-app-builder') . '</strong> ';
            echo !empty($form->success_message) ? esc_html($form->success_message) : __('Form submitted successfully!', 'db-app-builder');
            echo '</div>';

            // Add a link to submit another response if needed
            echo '<p><a href="' . esc_url(remove_query_arg(array('dab_form_success', 'form_id'))) . '">' . __('Submit another response', 'db-app-builder') . '</a></p>';

            // Return only the success message if the form was submitted successfully
            $output = ob_get_clean();
            return $output;
        }

        // Form HTML
        echo '<form method="post" enctype="multipart/form-data" class="dab-form" id="dab-form-' . esc_attr($form_id) . '" action="' . esc_url($_SERVER['REQUEST_URI']) . '">';

        // Add hidden fields
        echo '<input type="hidden" name="dab_form_id" value="' . esc_attr($form_id) . '">';
        echo '<input type="hidden" name="dab_form_submit" value="1">';
        echo wp_nonce_field('dab_form_submit', 'dab_form_nonce', true, false);



        // Form title
        if (!empty($form->form_title) && !empty($form->show_title)) {
            echo '<h3 class="dab-form-title">' . esc_html($form->form_title) . '</h3>';
        }

        // Form description
        if (!empty($form->form_description) && !empty($form->show_description)) {
            echo '<div class="dab-form-description">' . wp_kses_post($form->form_description) . '</div>';
        }

        // Check if form has payment fields
        $has_payment_field = false;
        foreach ($fields as $field) {
            if ($field->field_type === 'payment') {
                $has_payment_field = true;
                break;
            }
        }

        // Render fields
        foreach ($fields as $field) {
            $this->render_form_field($field);
        }

        // Submit button (hidden if form has payment fields)
        $submit_text = !empty($form->submit_button_text) ? $form->submit_button_text : __('Submit', 'db-app-builder');
        $submit_style = $has_payment_field ? ' style="display: none;"' : '';
        echo '<div class="dab-form-field dab-form-submit"' . $submit_style . '>';
        echo '<button type="submit" class="dab-submit-button">' . esc_html($submit_text) . '</button>';
        echo '</div>';

        echo '</form>';

        // Get the buffered content
        $output = ob_get_clean();

        return $output;
    }

    /**
     * Render a single form field
     *
     * @param object $field Field object
     */
    public function render_form_field($field) {
        $field_id = 'dab-field-' . esc_attr($field->id);
        $field_name = esc_attr($field->field_slug);
        $field_label = esc_html($field->field_label);
        $required = !empty($field->required) ? ' required' : '';
        $required_mark = !empty($field->required) ? ' <span class="dab-required">*</span>' : '';
        $placeholder = !empty($field->placeholder) ? esc_attr($field->placeholder) : '';

        echo '<div class="dab-form-field dab-field-type-' . esc_attr($field->field_type) . '" data-field="' . $field_name . '">';

        // Label
        echo '<label for="' . $field_id . '">' . $field_label . $required_mark . '</label>';

        // Field input based on type
        switch ($field->field_type) {
            case 'text':
                echo '<input type="text" id="' . $field_id . '" name="' . $field_name . '" placeholder="' . $placeholder . '"' . $required . '>';
                break;

            case 'email':
                echo '<input type="email" id="' . $field_id . '" name="' . $field_name . '" placeholder="' . $placeholder . '"' . $required . '>';
                break;

            case 'number':
                echo '<input type="number" id="' . $field_id . '" name="' . $field_name . '" placeholder="' . $placeholder . '"' . $required . '>';
                break;

            case 'textarea':
                echo '<textarea id="' . $field_id . '" name="' . $field_name . '" placeholder="' . $placeholder . '"' . $required . '></textarea>';
                break;

            case 'select':
                // Create enhanced dropdown wrapper
                echo '<div class="dab-enhanced-select-wrapper">';

                // Add dropdown icon
                echo '<div class="dab-select-icon"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg></div>';

                // The select element
                echo '<select id="' . $field_id . '" name="' . $field_name . '"' . $required . ' class="dab-enhanced-dropdown">';
                echo '<option value="">' . __('Select an option', 'db-app-builder') . '</option>';

                if (!empty($field->options)) {
                    // First, try to parse as a serialized array
                    $options = maybe_unserialize($field->options);

                    // If not an array, try to split by newlines
                    if (!is_array($options)) {
                        $options = explode("\n", $field->options);
                    }

                    // Process each option
                    if (is_array($options)) {
                        foreach ($options as $option_text) {
                            if (is_string($option_text)) {
                                $option_text = trim($option_text);
                                if (empty($option_text)) continue;

                                // Check if this is a key:value pair
                                $parts = explode(':', $option_text, 2);
                                if (count($parts) > 1) {
                                    $label = trim($parts[0]);
                                    $value = trim($parts[1]);
                                    echo '<option value="' . esc_attr($value) . '">' . esc_html($label) . '</option>';
                                } else {
                                    echo '<option value="' . esc_attr($option_text) . '">' . esc_html($option_text) . '</option>';
                                }
                            }
                        }
                    }


                }

                echo '</select>';

                // Add dropdown arrow
                echo '<div class="dab-select-arrow"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg></div>';

                echo '</div>'; // Close wrapper
                break;

            case 'checkbox':
                echo '<input type="checkbox" id="' . $field_id . '" name="' . $field_name . '" value="1"' . $required . '>';
                break;

            case 'radio':
                if (!empty($field->options)) {
                    // First, try to parse as a serialized array
                    $options = maybe_unserialize($field->options);

                    // If not an array, try to split by newlines
                    if (!is_array($options)) {
                        $options = explode("\n", $field->options);
                    }

                    // Process each option
                    if (is_array($options)) {
                        echo '<div class="dab-radio-group">';
                        foreach ($options as $index => $option_text) {
                            if (is_string($option_text)) {
                                $option_text = trim($option_text);
                                if (empty($option_text)) continue;

                                // Check if this is a key:value pair
                                $parts = explode(':', $option_text, 2);
                                if (count($parts) > 1) {
                                    $label = trim($parts[0]);
                                    $value = trim($parts[1]);
                                } else {
                                    $label = $option_text;
                                    $value = $option_text;
                                }

                                $option_id = $field_id . '-' . $index;
                                echo '<div class="dab-radio-option">';
                                echo '<input type="radio" id="' . $option_id . '" name="' . $field_name . '" value="' . esc_attr($value) . '"' . $required . '>';
                                echo '<label for="' . $option_id . '">' . esc_html($label) . '</label>';
                                echo '</div>';
                            }
                        }
                        echo '</div>';


                    }
                }
                break;

            case 'date':
                echo '<input type="date" id="' . $field_id . '" name="' . $field_name . '"' . $required . '>';
                break;

            case 'file':
                echo '<input type="file" id="' . $field_id . '" name="' . $field_name . '"' . $required . '>';
                break;

            case 'image':
                echo '<input type="file" id="' . $field_id . '" name="' . $field_name . '" accept="image/*"' . $required . '>';
                break;

            case 'relationship':
                echo $this->render_advanced_dropdown_field($field);
                break;

            case 'lookup':
                echo $this->render_lookup_field($field);
                break;

            case 'formula':
                echo $this->render_formula_field($field);
                break;

            case 'inline_table':
                echo $this->render_inline_table_field($field, '', $field_name);
                break;

            case 'payment':
                // Use the payment gateway renderer
                if (class_exists('DAB_Payment_Gateway')) {
                    DAB_Payment_Gateway::render_payment_field($field, '');
                } else {
                    echo '<div class="dab-error">Payment gateway not available</div>';
                }
                break;

            default:
                echo '<input type="text" id="' . $field_id . '" name="' . $field_name . '" placeholder="' . $placeholder . '"' . $required . '>';
                break;
        }

        echo '</div>';
    }

    /**
     * Render formula field
     *
     * @param object $field Field object
     * @param string $value Current value
     * @return string HTML for the formula field
     */
    public function render_formula_field($field, $value = '') {
        $field_id = 'dab-field-' . esc_attr($field->id);
        $field_name = esc_attr($field->field_slug);
        $formula = !empty($field->formula_expression) ? $field->formula_expression : '';
        $readonly = true; // Formula fields are always read-only

        // Create a more user-friendly display
        $output = '<div class="dab-formula-field-container">';

        // The actual input field (hidden or readonly)
        $output .= '<input type="text" id="' . $field_id . '" name="' . $field_name . '" value="' . esc_attr($value) . '"
            class="dab-formula-field" readonly data-formula="' . esc_attr($formula) . '">';

        // Add a visual calculator icon
        $output .= '<div class="dab-formula-icon" title="This is a calculated field"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="4" y="2" width="16" height="20" rx="2" ry="2"></rect><line x1="8" y1="6" x2="16" y2="6"></line><line x1="16" y1="14" x2="16" y2="18"></line><line x1="8" y1="14" x2="8" y2="18"></line><line x1="12" y1="14" x2="12" y2="18"></line><line x1="8" y1="10" x2="16" y2="10"></line></svg></div>';

        // Add a preview area that shows the calculation in real-time
        $output .= '<div class="dab-formula-preview">Result: ' . esc_html($value) . '</div>';

        // Create a human-readable formula display
        if (!empty($formula)) {
            $human_readable = $this->format_formula_for_display($formula);
            $output .= $human_readable;
        }

        $output .= '</div>';

        return $output;
    }

    /**
     * Render inline table field
     *
     * @param object $field Field object
     * @param string $value Current value
     * @param string $field_name Field name
     * @return string HTML for the inline table field
     */
    public function render_inline_table_field($field, $value = '', $field_name = '') {
        // Use the enhanced inline table renderer from Data Manager
        return DAB_Data_Manager::render_enhanced_inline_table_field($field, $value, $field_name);
    }

    /**
     * Format a formula expression for human-readable display
     *
     * @param string $formula The raw formula expression
     * @return string Formatted HTML for display
     */
    private function format_formula_for_display($formula) {
        global $wpdb;

        // Simple text-based formula display without HTML tags
        $simple_display = 'Formula: ';

        // Replace field references with field labels
        $simple_display .= preg_replace_callback('/{([^}]+)}/', function($matches) use ($wpdb) {
            $field_slug = $matches[1];
            $fields_table = $wpdb->prefix . 'dab_fields';

            // Try to find the field label
            $field_label = $wpdb->get_var($wpdb->prepare(
                "SELECT field_label FROM $fields_table WHERE field_slug = %s",
                $field_slug
            ));

            if ($field_label) {
                return $field_label;
            }

            return $field_slug;
        }, $formula);

        // Replace operators with more readable symbols
        $simple_display = $simple_display !== null ? (string)$simple_display : '';
        if ($simple_display !== '') {
            $simple_display = str_replace(
                array('*', '/'),
                array(' × ', ' ÷ '),
                $simple_display
            );

            // Add spaces around other operators for better readability
            $simple_display = str_replace(
                array('+', '-', '(', ')'),
                array(' + ', ' - ', ' ( ', ' ) '),
                $simple_display
            );
        }

        // Clean up any double spaces
        $simple_display = preg_replace('/\s+/', ' ', $simple_display);

        return '<div class="dab-formula-explanation">' . esc_html($simple_display) . '</div>';
    }

    /**
     * Render lookup field
     *
     * @param object $field Field object
     * @param string $value Selected value
     * @return string HTML for the lookup field
     */
    public function render_lookup_field($field, $value = '') {
        global $wpdb;

        // Get lookup table information
        $lookup_table_id = !empty($field->lookup_table_id) ? intval($field->lookup_table_id) : 0;

        if (!$lookup_table_id) {
            return '<div class="dab-error">No lookup table selected</div>';
        }

        // Get table information
        $tables_table = $wpdb->prefix . 'dab_tables';
        $lookup_table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d",
            $lookup_table_id
        ));

        if (!$lookup_table) {
            return '<div class="dab-error">Lookup table not found</div>';
        }

        // Get display column
        $display_column = !empty($field->lookup_display_column) ? $field->lookup_display_column : 'name';

        // Get data table name
        $lookup_table_name = $wpdb->prefix . 'dab_' . sanitize_title($lookup_table->table_slug);

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$lookup_table_name'") != $lookup_table_name) {
            return '<div class="dab-error">Lookup data table does not exist</div>';
        }

        // Check if display column exists
        $display_column_exists = $wpdb->get_var("SHOW COLUMNS FROM $lookup_table_name LIKE '$display_column'");
        if (!$display_column_exists) {
            // Try to find a suitable display column
            $potential_columns = ['name', 'title', 'label', 'description'];
            foreach ($potential_columns as $col) {
                if ($wpdb->get_var("SHOW COLUMNS FROM $lookup_table_name LIKE '$col'")) {
                    $display_column = $col;
                    break;
                }
            }

            // If still no suitable column found, use ID
            if (!$display_column_exists) {
                $display_column = 'id';
            }
        }

        // Get options from lookup table
        $options = $wpdb->get_results("SELECT id, $display_column AS display_value FROM $lookup_table_name ORDER BY $display_column ASC");

        if (empty($options)) {
            return '<div class="dab-error">No options found in lookup table</div>';
        }

        // Build select dropdown
        $field_id = 'dab-field-' . esc_attr($field->id);
        $field_name = esc_attr($field->field_slug);
        $required = !empty($field->required) ? ' required' : '';
        $placeholder = !empty($field->placeholder) ? esc_attr($field->placeholder) : __('Select...', 'db-app-builder');
        $is_searchable = true; // Make all lookup fields searchable for better UX

        // Create wrapper for enhanced dropdown
        $output = '<div class="dab-enhanced-select-wrapper">';

        // Add search icon
        $output .= '<div class="dab-select-icon"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg></div>';

        // The actual select element
        $output .= '<select id="' . $field_id . '" name="' . $field_name . '"' . $required . '
            class="dab-lookup-field' . ($is_searchable ? ' dab-searchable' : '') . '"
            data-placeholder="' . esc_attr($placeholder) . '">';
        $output .= '<option value="">' . esc_html($placeholder) . '</option>';

        // Group options if there are more than 10
        $group_options = count($options) > 10;
        $current_group = '';
        $groups = array();

        // If grouping, try to determine groups based on first character
        if ($group_options) {
            foreach ($options as $option) {
                $first_char = strtoupper(substr($option->display_value, 0, 1));
                if (ctype_alpha($first_char)) {
                    $groups[$first_char][] = $option;
                } else {
                    $groups['#'][] = $option;
                }
            }

            // Output options with optgroups
            foreach ($groups as $group => $group_options) {
                $output .= '<optgroup label="' . esc_attr($group) . '">';
                foreach ($group_options as $option) {
                    $selected = ($value == $option->id) ? ' selected' : '';
                    $output .= '<option value="' . esc_attr($option->id) . '"' . $selected . '>' . esc_html($option->display_value) . '</option>';
                }
                $output .= '</optgroup>';
            }
        } else {
            // Output options without grouping
            foreach ($options as $option) {
                $selected = ($value == $option->id) ? ' selected' : '';
                $output .= '<option value="' . esc_attr($option->id) . '"' . $selected . '>' . esc_html($option->display_value) . '</option>';
            }
        }

        $output .= '</select>';

        // Add dropdown arrow
        $output .= '<div class="dab-select-arrow"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg></div>';

        $output .= '</div>'; // Close wrapper

        return $output;
    }

    /**
     * Render advanced dropdown field
     *
     * @param object $field Field object
     * @param string $value Selected value
     * @return string HTML for the dropdown field
     */
    public function render_advanced_dropdown_field($field, $value = '') {
        global $wpdb;

        if (empty($field->related_table_id)) {
            return '<div class="dab-error">No related table selected for dropdown</div>';
        }

        // Get relationship information
        $relationships_table = $wpdb->prefix . 'dab_relationships';
        $relationship = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $relationships_table
            WHERE (table_a_id = %d AND table_b_id = %d)
            OR (table_a_id = %d AND table_b_id = %d)",
            $field->table_id, $field->related_table_id, $field->related_table_id, $field->table_id
        ));

        if (!$relationship) {
            return '<div class="dab-error">No relationship defined between tables</div>';
        }

        // Get target table information
        $tables_table = $wpdb->prefix . 'dab_tables';
        $related_table = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d",
            $field->related_table_id
        ));

        if (!$related_table) {
            return '<div class="dab-error">Related table not found</div>';
        }

        // Get display column
        $display_column = !empty($field->display_column) ? $field->display_column : 'id';

        // Check if this is an advanced dropdown
        $is_advanced = !empty($field->advanced_dropdown) && $field->advanced_dropdown === '1';
        $parent_field = !empty($field->parent_field) ? $field->parent_field : '';
        $searchable = !empty($field->searchable) && $field->searchable === '1';

        $dropdown_class = $is_advanced ? 'dab-advanced-dropdown-field' : '';
        $data_attrs = '';

        if ($is_advanced) {
            $data_attrs .= ' data-target-table="' . esc_attr($field->related_table_id) . '"';
            $data_attrs .= ' data-searchable="' . ($searchable ? 'true' : 'false') . '"';

            if ($parent_field) {
                $data_attrs .= ' data-parent-field="' . esc_attr($parent_field) . '"';
            }
        }

        // Get related records
        $related_table_name = $wpdb->prefix . 'dab_' . sanitize_title($related_table->table_slug);

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$related_table_name'") != $related_table_name) {
            return '<div class="dab-error">Related data table does not exist</div>';
        }

        // Check if display column exists
        $display_column_exists = $wpdb->get_var("SHOW COLUMNS FROM $related_table_name LIKE '$display_column'");
        if (!$display_column_exists) {
            $display_column = 'id'; // Fallback to ID if column doesn't exist
        }

        // Get options
        $options = $wpdb->get_results("SELECT id, $display_column AS display_value FROM $related_table_name ORDER BY $display_column ASC");

        $placeholder = !empty($field->placeholder) ? $field->placeholder : 'Select...';
        $searchable = true; // Make all relationship fields searchable for better UX

        // Create wrapper for enhanced dropdown
        $output = '<div class="dab-enhanced-select-wrapper">';

        // Add relationship icon
        $output .= '<div class="dab-select-icon"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path></svg></div>';

        // The actual select element
        $output .= '<select name="' . esc_attr($field->field_slug) . '" id="' . esc_attr($field->field_slug) . '"
            class="' . esc_attr($dropdown_class) . ' dab-relationship-field' . ($searchable ? ' dab-searchable' : '') . '"' . $data_attrs . '
            data-placeholder="' . esc_attr($placeholder) . '">';
        $output .= '<option value="">' . esc_html($placeholder) . '</option>';

        // Group options if there are more than 10
        $group_options = count($options) > 10;
        $current_group = '';
        $groups = array();

        // If grouping, try to determine groups based on first character
        if ($group_options) {
            foreach ($options as $option) {
                $first_char = strtoupper(substr($option->display_value, 0, 1));
                if (ctype_alpha($first_char)) {
                    $groups[$first_char][] = $option;
                } else {
                    $groups['#'][] = $option;
                }
            }

            // Output options with optgroups
            foreach ($groups as $group => $group_options) {
                $output .= '<optgroup label="' . esc_attr($group) . '">';
                foreach ($group_options as $option) {
                    $selected = ($value == $option->id) ? 'selected' : '';
                    $output .= '<option value="' . esc_attr($option->id) . '" ' . $selected . '>' .
                        esc_html($option->display_value) . '</option>';
                }
                $output .= '</optgroup>';
            }
        } else {
            // Output options without grouping
            foreach ($options as $option) {
                $selected = ($value == $option->id) ? 'selected' : '';
                $output .= '<option value="' . esc_attr($option->id) . '" ' . $selected . '>' .
                    esc_html($option->display_value) . '</option>';
            }
        }

        $output .= '</select>';

        // Add dropdown arrow
        $output .= '<div class="dab-select-arrow"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg></div>';

        $output .= '</div>'; // Close wrapper

        // Enqueue required scripts
        wp_enqueue_script('jquery');
        wp_enqueue_script('dab-advanced-dropdown', plugin_dir_url(dirname(__FILE__)) . 'assets/js/enhanced-dropdown.js', array('jquery'), null, true);
        wp_localize_script('dab-advanced-dropdown', 'dab_vars', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_dropdown_nonce')
        ));

        return $output;
    }

    /**
     * Enqueue form assets
     *
     * @param object $form Form object
     */
    public function enqueue_form_assets($form) {
        // Enqueue form styles
        wp_enqueue_style('dab-frontend-style', plugin_dir_url(dirname(__FILE__)) . 'assets/css/frontend-style.css');

        // Enqueue enhanced dropdown styles
        wp_enqueue_style('dab-enhanced-dropdown-style', plugin_dir_url(dirname(__FILE__)) . 'assets/css/enhanced-dropdown.css');

        // Enqueue form scripts
        wp_enqueue_script('jquery');
        wp_enqueue_script('dab-form-validation', plugin_dir_url(dirname(__FILE__)) . 'assets/js/form-validation.js', array('jquery'), null, true);
        wp_enqueue_script('dab-form-logic', plugin_dir_url(dirname(__FILE__)) . 'assets/js/form-logic.js', array('jquery'), null, true);
        wp_enqueue_script('dab-enhanced-dropdown', plugin_dir_url(dirname(__FILE__)) . 'assets/js/enhanced-dropdown.js', array('jquery'), null, true);



        // Localize script with form data
        if (!empty($form->conditional_logic)) {
            wp_localize_script('dab-form-logic', 'dabLogic', array(
                'rules' => json_decode($form->conditional_logic, true)
            ));
        }

        // Enqueue formula calculator if needed
        $fields = $this->get_form_fields($form->id);
        $has_formula = false;
        $formulas = array();

        foreach ($fields as $field) {
            if ($field->field_type === 'formula' && !empty($field->formula_expression)) {
                $has_formula = true;
                $formulas[$field->field_slug] = $field->formula_expression;
            }
        }

        if ($has_formula) {
            // Use the enhanced formula calculator
            wp_enqueue_script('dab-formula-calc', plugin_dir_url(dirname(__FILE__)) . 'assets/js/enhanced-formula-calc.js', array('jquery'), null, true);
            wp_localize_script('dab-formula-calc', 'dabFormulas', $formulas);

            // Add CSS for formula field indicators
            wp_add_inline_style('dab-frontend-style', '
                .dab-used-in-formula {
                    position: relative;
                }
                .dab-formula-indicator {
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #4a90e2;
                    z-index: 1;
                    cursor: help;
                }
                .dab-formula-indicator:hover::after {
                    content: "This field is used in a formula calculation";
                    position: absolute;
                    background: #333;
                    color: #fff;
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-size: 12px;
                    white-space: nowrap;
                    z-index: 100;
                    top: -30px;
                    right: 0;
                }
            ');
        }
    }

    /**
     * Process form submission
     */
    public function process_form_submission() {
        // Check if this is a form submission
        if (!isset($_POST['dab_form_id']) || !isset($_POST['dab_form_submit'])) {
            return;
        }



        // Verify nonce
        if (!isset($_POST['dab_form_nonce']) || !wp_verify_nonce($_POST['dab_form_nonce'], 'dab_form_submit')) {
            wp_die(__('Security check failed', 'db-app-builder'));
        }

        $form_id = intval($_POST['dab_form_id']);

        // Get form data
        $form = $this->get_form($form_id);
        if (!$form) {
            wp_die(__('Form not found', 'db-app-builder'));
        }

        // Get form fields
        $fields = $this->get_form_fields($form_id);
        if (empty($fields)) {
            wp_die(__('No fields found for this form', 'db-app-builder'));
        }

        // Validate form data
        $data = array();
        $errors = array();

        foreach ($fields as $field) {
            $field_name = $field->field_slug;
            $field_label = $field->field_label;
            $required = !empty($field->required);

            // Skip file fields for now, we'll handle them separately
            if (in_array($field->field_type, array('file', 'image'))) {
                continue;
            }

            // Check if field is required
            if ($required && (!isset($_POST[$field_name]) || $_POST[$field_name] === '')) {
                $errors[] = sprintf(__('%s is required', 'db-app-builder'), $field_label);
                continue;
            }

            // Get field value
            $value = isset($_POST[$field_name]) ? $_POST[$field_name] : '';

            // Validate and sanitize based on field type
            switch ($field->field_type) {
                case 'email':
                    if (!empty($value) && !is_email($value)) {
                        $errors[] = sprintf(__('%s must be a valid email address', 'db-app-builder'), $field_label);
                    }
                    $data[$field_name] = sanitize_email($value);
                    break;

                case 'number':
                    if (!empty($value) && !is_numeric($value)) {
                        $errors[] = sprintf(__('%s must be a number', 'db-app-builder'), $field_label);
                    }
                    $data[$field_name] = floatval($value);
                    break;

                case 'checkbox':
                    $data[$field_name] = !empty($value) ? 1 : 0;
                    break;

                case 'textarea':
                    $data[$field_name] = sanitize_textarea_field($value);
                    break;

                case 'lookup':
                case 'relationship':
                    // For lookup and relationship fields, store the ID as an integer
                    $data[$field_name] = !empty($value) ? intval($value) : null;
                    break;

                default:
                    $data[$field_name] = sanitize_text_field($value);
                    break;
            }
        }

        // Handle file uploads
        foreach ($fields as $field) {
            if (!in_array($field->field_type, array('file', 'image'))) {
                continue;
            }

            $field_name = $field->field_slug;
            $field_label = $field->field_label;
            $required = !empty($field->required);

            // Check if file is required
            if ($required && empty($_FILES[$field_name]['name'])) {
                $errors[] = sprintf(__('%s is required', 'db-app-builder'), $field_label);
                continue;
            }

            // Process file upload
            if (!empty($_FILES[$field_name]['name'])) {
                $file = $this->handle_file_upload($field_name, $field->field_type === 'image');

                if (is_wp_error($file)) {
                    $errors[] = sprintf(__('Error uploading %s: %s', 'db-app-builder'), $field_label, $file->get_error_message());
                } else {
                    $data[$field_name] = $file;
                }
            }
        }

        // If there are errors, redirect back with error messages
        if (!empty($errors)) {
            $redirect_url = add_query_arg(
                array(
                    'dab_form_error' => 1,
                    'form_id' => $form_id,
                    'errors' => urlencode(implode('|', $errors))
                ),
                wp_get_referer() ?: home_url()
            );

            wp_safe_redirect($redirect_url);
            exit;
        }

        // Add timestamps
        $data['created_at'] = current_time('mysql');
        $data['updated_at'] = current_time('mysql');

        // Add user ID if user is logged in
        if (is_user_logged_in()) {
            $data['user_id'] = get_current_user_id();
        }

        // Get table information
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d",
            $form->table_id
        ));

        if (!$table_info) {
            wp_die(__('Table not found', 'db-app-builder'));
        }

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Insert data into table
        $result = $wpdb->insert($data_table, $data);

        if ($result === false) {
            wp_die(__('Error saving form data', 'db-app-builder') . ': ' . $wpdb->last_error);
        }

        // Get the ID of the inserted record
        $record_id = $wpdb->insert_id;

        // Trigger action for plugins to process form data
        do_action('dab_save_form_data', $record_id, $_POST, $fields);

        // Update payment records with the record ID if there are any payment fields
        if (class_exists('DAB_Payment_Gateway')) {
            foreach ($fields as $field) {
                if ($field->field_type === 'payment' && isset($data[$field->field_slug]) && !empty($data[$field->field_slug])) {
                    $transaction_id = $data[$field->field_slug];
                    DAB_Payment_Gateway::update_payment_record_id($transaction_id, $record_id);
                }
            }
        }

        // Send email notification if configured
        if (!empty($form->notify_email) && !empty($form->notify_message)) {
            $this->send_email_notification($form, $data, $record_id);
        }

        // Check for conditional email notification
        if (isset($_POST['conditional_email']) && !empty($_POST['conditional_email'])) {
            $conditional_email = sanitize_email($_POST['conditional_email']);
            if (!empty($conditional_email) && !empty($form->notify_message)) {
                $this->send_email_notification($form, $data, $record_id, $conditional_email);
            }
        }

        // Send data to Google Sheets if integration is enabled
        if (class_exists('DAB_Google_Sheets_Integration') && !empty($form->google_sheets_enabled)) {
            DAB_Google_Sheets_Integration::send_to_google_sheets($form_id, $data);
        }

        // Send data to Zapier if integration is enabled
        if (class_exists('DAB_Zapier_Integration') && !empty($form->zapier_enabled)) {
            DAB_Zapier_Integration::send_to_zapier($form_id, $data);
        }

        // Redirect to success page
        $redirect_url = '';

        if (!empty($form->success_redirect_url)) {
            $redirect_url = $form->success_redirect_url;
        } else {
            // Get the current page URL without any query parameters
            $current_url = strtok($_SERVER["REQUEST_URI"], '?');
            $current_url = home_url($current_url);

            // Redirect back to the same page with success parameter
            $redirect_url = add_query_arg(
                array(
                    'dab_form_success' => 1,
                    'form_id' => $form_id
                ),
                $current_url
            );
        }

        // Apply filters to allow customization of redirect URL
        $redirect_url = apply_filters('dab_form_submission_redirect', $redirect_url, $form_id, $record_id);

        // Redirect
        wp_safe_redirect($redirect_url);
        exit;
    }

    /**
     * Process form submission via AJAX
     */
    public function ajax_process_form_submission() {
        // Check if this is a form submission
        if (!isset($_POST['dab_form_id'])) {
            wp_send_json_error(array('message' => __('Invalid form submission', 'db-app-builder')));
        }

        // Verify nonce
        if (!isset($_POST['dab_form_nonce']) || !wp_verify_nonce($_POST['dab_form_nonce'], 'dab_form_submit')) {
            wp_send_json_error(array('message' => __('Security check failed', 'db-app-builder')));
        }

        $form_id = intval($_POST['dab_form_id']);

        // Get form data
        $form = $this->get_form($form_id);
        if (!$form) {
            wp_send_json_error(array('message' => __('Form not found', 'db-app-builder')));
        }

        // Get form fields
        $fields = $this->get_form_fields($form_id);
        if (empty($fields)) {
            wp_send_json_error(array('message' => __('No fields found for this form', 'db-app-builder')));
        }

        // Validate form data
        $data = array();
        $errors = array();

        foreach ($fields as $field) {
            $field_name = $field->field_slug;
            $field_label = $field->field_label;
            $required = !empty($field->required);

            // Skip file fields for now, we'll handle them separately
            if (in_array($field->field_type, array('file', 'image'))) {
                continue;
            }

            // Check if field is required
            if ($required && (!isset($_POST[$field_name]) || $_POST[$field_name] === '')) {
                $errors[] = sprintf(__('%s is required', 'db-app-builder'), $field_label);
                continue;
            }

            // Get field value
            $value = isset($_POST[$field_name]) ? $_POST[$field_name] : '';

            // Validate and sanitize based on field type
            switch ($field->field_type) {
                case 'email':
                    if (!empty($value) && !is_email($value)) {
                        $errors[] = sprintf(__('%s must be a valid email address', 'db-app-builder'), $field_label);
                    }
                    $data[$field_name] = sanitize_email($value);
                    break;

                case 'number':
                    if (!empty($value) && !is_numeric($value)) {
                        $errors[] = sprintf(__('%s must be a number', 'db-app-builder'), $field_label);
                    }
                    $data[$field_name] = floatval($value);
                    break;

                case 'checkbox':
                    $data[$field_name] = !empty($value) ? 1 : 0;
                    break;

                case 'textarea':
                    $data[$field_name] = sanitize_textarea_field($value);
                    break;

                case 'lookup':
                case 'relationship':
                    // For lookup and relationship fields, store the ID as an integer
                    $data[$field_name] = !empty($value) ? intval($value) : null;
                    break;

                default:
                    $data[$field_name] = sanitize_text_field($value);
                    break;
            }
        }

        // Handle file uploads
        foreach ($fields as $field) {
            if (!in_array($field->field_type, array('file', 'image'))) {
                continue;
            }

            $field_name = $field->field_slug;
            $field_label = $field->field_label;
            $required = !empty($field->required);

            // Check if file is required
            if ($required && empty($_FILES[$field_name]['name'])) {
                $errors[] = sprintf(__('%s is required', 'db-app-builder'), $field_label);
                continue;
            }

            // Process file upload
            if (!empty($_FILES[$field_name]['name'])) {
                $file = $this->handle_file_upload($field_name, $field->field_type === 'image');

                if (is_wp_error($file)) {
                    $errors[] = sprintf(__('Error uploading %s: %s', 'db-app-builder'), $field_label, $file->get_error_message());
                } else {
                    $data[$field_name] = $file;
                }
            }
        }

        // If there are errors, return them
        if (!empty($errors)) {
            wp_send_json_error(array('message' => __('Validation failed', 'db-app-builder'), 'errors' => $errors));
        }

        // Add timestamps
        $data['created_at'] = current_time('mysql');
        $data['updated_at'] = current_time('mysql');

        // Add user ID if user is logged in
        if (is_user_logged_in()) {
            $data['user_id'] = get_current_user_id();
        }

        // Get table information
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tables_table WHERE id = %d",
            $form->table_id
        ));

        if (!$table_info) {
            wp_send_json_error(array('message' => __('Table not found', 'db-app-builder')));
        }

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Insert data into table
        $result = $wpdb->insert($data_table, $data);

        if ($result === false) {
            wp_send_json_error(array('message' => __('Error saving form data', 'db-app-builder') . ': ' . $wpdb->last_error));
        }

        // Get the ID of the inserted record
        $record_id = $wpdb->insert_id;

        // Trigger action for plugins to process form data
        do_action('dab_save_form_data', $record_id, $_POST, $fields);

        // Update payment records with the record ID if there are any payment fields
        if (class_exists('DAB_Payment_Gateway')) {
            foreach ($fields as $field) {
                if ($field->field_type === 'payment' && isset($data[$field->field_slug]) && !empty($data[$field->field_slug])) {
                    $transaction_id = $data[$field->field_slug];
                    DAB_Payment_Gateway::update_payment_record_id($transaction_id, $record_id);
                }
            }
        }

        // Send email notification if configured
        if (!empty($form->notify_email) && !empty($form->notify_message)) {
            $this->send_email_notification($form, $data, $record_id);
        }

        // Check for conditional email notification
        if (isset($_POST['conditional_email']) && !empty($_POST['conditional_email'])) {
            $conditional_email = sanitize_email($_POST['conditional_email']);
            if (!empty($conditional_email) && !empty($form->notify_message)) {
                $this->send_email_notification($form, $data, $record_id, $conditional_email);
            }
        }

        // Send data to Google Sheets if integration is enabled
        if (class_exists('DAB_Google_Sheets_Integration') && !empty($form->google_sheets_enabled)) {
            DAB_Google_Sheets_Integration::send_to_google_sheets($form_id, $data);
        }

        // Send data to Zapier if integration is enabled
        if (class_exists('DAB_Zapier_Integration') && !empty($form->zapier_enabled)) {
            DAB_Zapier_Integration::send_to_zapier($form_id, $data);
        }

        // Return success response
        wp_send_json_success(array(
            'message' => __('Form submitted successfully', 'db-app-builder'),
            'record_id' => $record_id
        ));
    }

    /**
     * Handle file upload
     *
     * @param string $field_name Field name
     * @param bool $is_image Whether the file is an image
     * @return string|WP_Error File URL or WP_Error on failure
     */
    private function handle_file_upload($field_name, $is_image = false) {
        if (empty($_FILES[$field_name]['name'])) {
            return '';
        }

        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');

        // Check file type
        if ($is_image) {
            $file_type = wp_check_filetype($_FILES[$field_name]['name']);
            $allowed_types = array('jpg', 'jpeg', 'png', 'gif');

            if (!in_array(strtolower($file_type['ext']), $allowed_types)) {
                return new WP_Error('invalid_file_type', __('Invalid file type. Only JPG, PNG, and GIF files are allowed.', 'db-app-builder'));
            }
        }

        // Upload the file
        $attachment_id = media_handle_upload($field_name, 0);

        if (is_wp_error($attachment_id)) {
            return $attachment_id;
        }

        return $attachment_id;
    }

    /**
     * Send email notification
     *
     * @param object $form Form object
     * @param array $data Form data
     * @param int $record_id Record ID
     * @param string $conditional_email Optional. Email address for conditional notifications.
     */
    private function send_email_notification($form, $data, $record_id, $conditional_email = '') {
        // Determine recipient email
        $to = !empty($conditional_email) ? $conditional_email : $form->notify_email;
        $subject = sprintf(__('New form submission: %s', 'db-app-builder'), $form->form_name);
        $message = $form->notify_message;

        // Replace placeholders with actual values
        foreach ($data as $key => $value) {
            // Handle file/image fields
            if (is_numeric($value) && in_array($key, array_column($this->get_form_fields($form->id), 'field_slug'))) {
                $attachment_url = wp_get_attachment_url($value);
                if ($attachment_url) {
                    $value = $attachment_url;
                }
            }

            // Make sure value is not null before using it in str_replace
            $safe_value = $value !== null ? (string)$value : '';
            $message = str_replace('{{' . $key . '}}', $safe_value, $message);
        }

        // Add record ID
        if ($message !== null && $message !== '') {
            $message = str_replace('{{record_id}}', $record_id, $message);

            // Add admin link
            $admin_url = admin_url('admin.php?page=dab_data&table_id=' . $form->table_id . '&action=edit&id=' . $record_id);
            $message = str_replace('{{admin_link}}', $admin_url, $message);
        }

        // Send email
        $headers = array('Content-Type: text/html; charset=UTF-8');
        wp_mail($to, $subject, $message, $headers);
    }

    /**
     * Get debug information for a form
     *
     * @param int $form_id Form ID
     * @param object $form Form object
     * @param array $fields Form fields
     * @return string Debug information HTML
     */
    private function get_form_debug_info($form_id, $form, $fields) {
        // Return empty string - debug info removed for production
        return '';
    }
}