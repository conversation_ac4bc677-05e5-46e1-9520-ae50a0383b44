<?php
/**
 * Template Manager Class
 *
 * Manages business application templates for one-click installation
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

class DAB_Template_Manager {

    /**
     * Initialize the Template Manager
     */
    public static function init() {
        add_action('wp_ajax_dab_install_template', array(__CLASS__, 'install_template'));
        add_action('wp_ajax_dab_get_template_preview', array(__CLASS__, 'get_template_preview'));
        add_action('wp_ajax_dab_search_templates', array(__CLASS__, 'search_templates'));
        add_action('wp_ajax_dab_delete_template_installation', array(__CLASS__, 'delete_template_installation'));

        // Add test AJAX handler for debugging
        add_action('wp_ajax_dab_test_ajax', array(__CLASS__, 'test_ajax_connectivity'));

        // Add admin menu with higher priority to ensure it runs after the main menu
        add_action('admin_menu', array(__CLASS__, 'add_admin_menu'), 20);

        // Enqueue scripts
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));

        // Ensure templates are loaded on admin init
        add_action('admin_init', array(__CLASS__, 'ensure_templates_loaded'));
    }

    /**
     * Ensure templates are loaded in database
     */
    public static function ensure_templates_loaded() {
        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_app_templates';

        // Check if templates table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$templates_table'") === $templates_table;

        if (!$table_exists) {
            self::create_tables();
            return;
        }

        // Check if we have templates (we have 19 templates total)
        $template_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_system = 1");

        if ($template_count < 19) {
            // Re-insert templates if missing
            self::insert_default_templates();

            // Log template loading for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                $new_count = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table WHERE is_system = 1");
                error_log("DAB Templates: Loaded $new_count templates (was $template_count)");
            }
        }
    }

    /**
     * Create database tables for templates
     */
    public static function create_tables() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Templates table
        $templates_table = $wpdb->prefix . 'dab_app_templates';
        $sql_templates = "CREATE TABLE IF NOT EXISTS $templates_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            template_key VARCHAR(100) NOT NULL UNIQUE,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            category VARCHAR(100) NOT NULL,
            subcategory VARCHAR(100),
            icon VARCHAR(100),
            preview_image VARCHAR(255),
            template_config LONGTEXT NOT NULL,
            sample_data LONGTEXT,
            requirements LONGTEXT,
            version VARCHAR(20) DEFAULT '1.0.0',
            is_system TINYINT(1) DEFAULT 1,
            is_active TINYINT(1) DEFAULT 1,
            install_count INT DEFAULT 0,
            rating DECIMAL(3,2) DEFAULT 0.00,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_template_key (template_key),
            KEY idx_category (category),
            KEY idx_is_active (is_active),
            KEY idx_is_system (is_system)
        ) $charset_collate;";

        // Template installations table
        $installations_table = $wpdb->prefix . 'dab_template_installations';
        $sql_installations = "CREATE TABLE IF NOT EXISTS $installations_table (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            template_id BIGINT(20) UNSIGNED NOT NULL,
            template_key VARCHAR(100) NOT NULL,
            installation_name VARCHAR(255) NOT NULL,
            installed_tables LONGTEXT,
            installed_forms LONGTEXT,
            installed_views LONGTEXT,
            installed_workflows LONGTEXT,
            installation_data LONGTEXT,
            status VARCHAR(50) DEFAULT 'active',
            installed_by BIGINT(20) UNSIGNED,
            installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_template_id (template_id),
            KEY idx_template_key (template_key),
            KEY idx_status (status),
            KEY idx_installed_by (installed_by)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_templates);
        dbDelta($sql_installations);

        // Insert default templates
        self::insert_default_templates();
    }

    /**
     * Add admin menu for template management
     */
    public static function add_admin_menu() {
        add_submenu_page(
            'dab_dashboard',
            __('App Templates', 'db-app-builder'),
            __('🏗️ App Templates', 'db-app-builder'),
            'manage_options',
            'dab_app_templates',
            array(__CLASS__, 'render_templates_page')
        );

        // Add setup page for troubleshooting
        add_submenu_page(
            null, // Hidden from menu
            __('Template Setup', 'db-app-builder'),
            __('Template Setup', 'db-app-builder'),
            'manage_options',
            'dab_template_setup',
            array(__CLASS__, 'render_setup_page')
        );
    }

    /**
     * Render setup page for troubleshooting
     */
    public static function render_setup_page() {
        include plugin_dir_path(__FILE__) . '../admin/template-setup.php';
    }

    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts($hook) {
        // Ensure hook is not null to prevent deprecated warnings
        $hook = $hook !== null ? (string)$hook : '';

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Template Manager: enqueue_scripts called with hook: ' . $hook);
        }

        // Load on template pages and also check for page parameter
        $should_load = false;

        if ($hook !== '' && strpos($hook, 'dab_app_templates') !== false) {
            $should_load = true;
        }

        // Also check if we're on the templates page via GET parameter
        if (isset($_GET['page']) && $_GET['page'] === 'dab_app_templates') {
            $should_load = true;
        }

        if ($should_load) {
            // Enqueue jQuery if not already loaded
            wp_enqueue_script('jquery');

            wp_enqueue_script(
                'dab-templates',
                DAB_PLUGIN_URL . 'assets/js/templates.js',
                array('jquery'),
                DAB_VERSION . '-' . time(), // Add timestamp for cache busting during development
                true
            );

            wp_enqueue_style(
                'dab-templates',
                DAB_PLUGIN_URL . 'assets/css/templates.css',
                array(),
                DAB_VERSION . '-' . time(), // Add timestamp for cache busting during development
            );

            // Localize script with enhanced data
            $localize_data = array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('dab_templates_nonce'),
                'debug' => defined('WP_DEBUG') && WP_DEBUG,
                'messages' => array(
                    'installing' => __('Installing template...', 'db-app-builder'),
                    'install_success' => __('Template installed successfully!', 'db-app-builder'),
                    'install_error' => __('Failed to install template. Please try again.', 'db-app-builder'),
                    'installation_name_required' => __('Please enter an installation name.', 'db-app-builder'),
                    'confirm_delete' => __('Are you sure you want to delete the installation "%s"? This action cannot be undone.', 'db-app-builder'),
                    'preview_error' => __('Failed to load template preview.', 'db-app-builder'),
                    'ajax_error' => __('Request failed. Please check your connection and try again.', 'db-app-builder')
                )
            );

            wp_localize_script('dab-templates', 'dab_templates', $localize_data);

            // Debug logging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Manager: Scripts and styles enqueued successfully');
                error_log('DAB Template Manager: Localized data: ' . print_r($localize_data, true));
            }
        }
    }

    /**
     * Render templates page
     */
    public static function render_templates_page() {
        global $wpdb;

        // Ensure templates are loaded
        self::ensure_templates_loaded();

        // Get template categories
        $templates_table = $wpdb->prefix . 'dab_app_templates';
        $categories = $wpdb->get_results(
            "SELECT DISTINCT category, COUNT(*) as template_count
             FROM $templates_table
             WHERE is_active = 1
             GROUP BY category
             ORDER BY category"
        );

        // Get all templates
        $templates = $wpdb->get_results(
            "SELECT * FROM $templates_table
             WHERE is_active = 1
             ORDER BY category, name"
        );

        // Get installed templates
        $installations_table = $wpdb->prefix . 'dab_template_installations';
        $installed_templates = $wpdb->get_results(
            "SELECT * FROM $installations_table
             WHERE status = 'active'
             ORDER BY installed_at DESC"
        );

        // Debug information
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Templates Page: Found ' . count($templates) . ' templates and ' . count($categories) . ' categories');
        }

        include plugin_dir_path(__FILE__) . '../admin/page-templates.php';
    }

    /**
     * Install a template via AJAX
     */
    public static function install_template() {
        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Template Install: Request received');
            error_log('DAB Template Install: POST data: ' . print_r($_POST, true));
        }

        // Check if required POST data exists
        if (!isset($_POST['nonce'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Install: Nonce not provided');
            }
            wp_send_json_error(__('Security nonce not provided', 'db-app-builder'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'dab_templates_nonce')) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Install: Nonce verification failed');
            }
            wp_send_json_error(__('Security check failed', 'db-app-builder'));
        }

        if (!current_user_can('manage_options')) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Install: Insufficient permissions');
            }
            wp_send_json_error(__('Insufficient permissions', 'db-app-builder'));
        }

        // Check required parameters
        if (!isset($_POST['template_id']) || !isset($_POST['installation_name'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Install: Missing required parameters');
            }
            wp_send_json_error(__('Required parameters missing', 'db-app-builder'));
        }

        $template_id = intval($_POST['template_id']);
        $installation_name = sanitize_text_field($_POST['installation_name']);

        if (empty($template_id) || empty($installation_name)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Install: Empty template ID or installation name');
            }
            wp_send_json_error(__('Template ID and installation name are required', 'db-app-builder'));
        }

        // Get template data
        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_app_templates';
        $template = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $templates_table WHERE id = %d AND is_active = 1",
            $template_id
        ));

        if (!$template) {
            wp_send_json_error(__('Template not found', 'db-app-builder'));
        }

        // Ensure database schema is up to date before installation
        try {
            if (class_exists('DAB_DB_Manager')) {
                DAB_DB_Manager::ensure_advanced_field_columns_exist();
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('DAB Template Install: Database schema updated');
                }
            }
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Install: Database schema update failed: ' . $e->getMessage());
            }
        }

        // Install the template
        $result = self::process_template_installation($template, $installation_name);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        // Update install count
        $wpdb->query($wpdb->prepare(
            "UPDATE $templates_table SET install_count = install_count + 1 WHERE id = %d",
            $template_id
        ));

        wp_send_json_success(array(
            'message' => __('Template installed successfully!', 'db-app-builder'),
            'installation_id' => $result['installation_id'],
            'redirect_url' => admin_url('admin.php?page=dab_tables')
        ));
    }

    /**
     * Process template installation
     */
    private static function process_template_installation($template, $installation_name) {
        global $wpdb;

        $template_config = json_decode($template->template_config, true);
        if (!$template_config) {
            return new WP_Error('invalid_config', __('Invalid template configuration', 'db-app-builder'));
        }

        $installation_data = array(
            'tables' => array(),
            'forms' => array(),
            'views' => array(),
            'workflows' => array()
        );

        try {
            // Start transaction
            $wpdb->query('START TRANSACTION');

            // Install tables and fields
            if (isset($template_config['tables'])) {
                foreach ($template_config['tables'] as $table_config) {
                    $table_result = self::install_template_table($table_config, $installation_name);
                    if (is_wp_error($table_result)) {
                        throw new Exception($table_result->get_error_message());
                    }
                    $installation_data['tables'][] = $table_result;
                }
            }

            // Install forms
            if (isset($template_config['forms'])) {
                foreach ($template_config['forms'] as $form_config) {
                    $form_result = self::install_template_form($form_config, $installation_data['tables']);
                    if (is_wp_error($form_result)) {
                        throw new Exception($form_result->get_error_message());
                    }
                    $installation_data['forms'][] = $form_result;
                }
            }

            // Install views
            if (isset($template_config['views'])) {
                foreach ($template_config['views'] as $view_config) {
                    $view_result = self::install_template_view($view_config, $installation_data['tables']);
                    if (is_wp_error($view_result)) {
                        throw new Exception($view_result->get_error_message());
                    }
                    $installation_data['views'][] = $view_result;
                }
            }

            // Record installation
            $installations_table = $wpdb->prefix . 'dab_template_installations';
            $wpdb->insert($installations_table, array(
                'template_id' => $template->id,
                'template_key' => $template->template_key,
                'installation_name' => $installation_name,
                'installed_tables' => json_encode($installation_data['tables']),
                'installed_forms' => json_encode($installation_data['forms']),
                'installed_views' => json_encode($installation_data['views']),
                'installed_workflows' => json_encode($installation_data['workflows']),
                'installation_data' => json_encode($installation_data),
                'installed_by' => get_current_user_id()
            ));

            $installation_id = $wpdb->insert_id;

            // Commit transaction
            $wpdb->query('COMMIT');

            return array(
                'installation_id' => $installation_id,
                'data' => $installation_data
            );

        } catch (Exception $e) {
            // Rollback transaction
            $wpdb->query('ROLLBACK');
            return new WP_Error('installation_failed', $e->getMessage());
        }
    }

    /**
     * Install template table
     */
    private static function install_template_table($table_config, $installation_name) {
        global $wpdb;

        $tables_table = $wpdb->prefix . 'dab_tables';
        $fields_table = $wpdb->prefix . 'dab_fields';

        // Create table with installation prefix - sanitize the installation name
        $sanitized_installation_name = sanitize_title($installation_name);
        $table_slug = $sanitized_installation_name . '_' . $table_config['slug'];
        $table_label = $table_config['label'];

        // Check if table already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $tables_table WHERE table_slug = %s",
            $table_slug
        ));

        if ($existing) {
            return new WP_Error('table_exists', sprintf(__('Table %s already exists', 'db-app-builder'), $table_slug));
        }

        // Insert table
        $wpdb->insert($tables_table, array(
            'table_label' => $table_label,
            'table_slug' => $table_slug,
            'description' => $table_config['description'] ?? '',
            'created_at' => current_time('mysql')
        ));

        $table_id = $wpdb->insert_id;

        // Create data table
        DAB_DB_Manager::create_data_table($table_slug);

        // Verify data table was created
        $data_table_name = $wpdb->prefix . 'dab_' . sanitize_title($table_slug);
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$data_table_name'");

        if (!$table_exists) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Install: Failed to create data table: ' . $data_table_name);
            }
            return new WP_Error('table_creation_failed', sprintf(__('Failed to create data table %s', 'db-app-builder'), $data_table_name));
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Template Install: Data table created successfully: ' . $data_table_name);
        }

        // Install fields
        if (isset($table_config['fields'])) {
            // Check which options column exists in the database
            $table_columns = $wpdb->get_col("SHOW COLUMNS FROM $fields_table");

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Install: Available columns in fields table: ' . implode(', ', $table_columns));
            }

            foreach ($table_config['fields'] as $index => $field_config) {
                $field_data = array(
                    'table_id' => $table_id,
                    'field_label' => $field_config['label'],
                    'field_slug' => $field_config['slug'],
                    'field_type' => $field_config['type'],
                    'required' => $field_config['required'] ?? 0,
                    'placeholder' => $field_config['placeholder'] ?? '',
                    'field_order' => $index + 1,
                    'created_at' => current_time('mysql')
                );

                // Handle field options - check which column exists
                if (isset($field_config['options'])) {
                    $options_value = json_encode($field_config['options']);
                    if (in_array('field_options', $table_columns)) {
                        $field_data['field_options'] = $options_value;
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log('DAB Template Install: Using field_options column for field: ' . $field_config['slug']);
                        }
                    } elseif (in_array('options', $table_columns)) {
                        $field_data['options'] = $options_value;
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log('DAB Template Install: Using options column for field: ' . $field_config['slug']);
                        }
                    } else {
                        if (defined('WP_DEBUG') && WP_DEBUG) {
                            error_log('DAB Template Install: No options column found for field: ' . $field_config['slug']);
                        }
                    }
                }

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('DAB Template Install: Inserting field data: ' . print_r($field_data, true));
                }

                $field_insert_result = $wpdb->insert($fields_table, $field_data);

                if ($field_insert_result === false) {
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log('DAB Template Install: Failed to insert field: ' . $field_config['slug'] . ' - Error: ' . $wpdb->last_error);
                    }
                    return new WP_Error('field_insert_failed', sprintf(__('Failed to insert field %s: %s', 'db-app-builder'), $field_config['slug'], $wpdb->last_error));
                }

                // Create column in data table
                $column_type = DAB_DB_Manager::get_column_type_for_field($field_config['type']);
                try {
                    DAB_DB_Manager::ensure_column_exists(
                        $wpdb->prefix . 'dab_' . $table_slug,
                        $field_config['slug'],
                        $column_type
                    );

                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log('DAB Template Install: Column created/verified for field: ' . $field_config['slug']);
                    }
                } catch (Exception $e) {
                    if (defined('WP_DEBUG') && WP_DEBUG) {
                        error_log('DAB Template Install: Failed to create column for field: ' . $field_config['slug'] . ' - Error: ' . $e->getMessage());
                    }
                    // Don't fail the entire installation for column creation issues
                }
            }
        }

        return array(
            'table_id' => $table_id,
            'table_slug' => $table_slug,
            'table_label' => $table_label
        );
    }

    /**
     * Install template form
     */
    private static function install_template_form($form_config, $installed_tables) {
        global $wpdb;

        $forms_table = $wpdb->prefix . 'dab_forms';

        // Find the target table
        $target_table = null;
        foreach ($installed_tables as $table) {
            if ($table['table_slug'] === $form_config['table_slug'] ||
                str_ends_with($table['table_slug'], '_' . $form_config['table_slug'])) {
                $target_table = $table;
                break;
            }
        }

        if (!$target_table) {
            return new WP_Error('table_not_found', sprintf(__('Target table %s not found', 'db-app-builder'), $form_config['table_slug']));
        }

        // Insert form
        $wpdb->insert($forms_table, array(
            'form_name' => $form_config['name'],
            'table_id' => $target_table['table_id'],
            'fields' => json_encode($form_config['fields'] ?? array()),
            'notify_email' => $form_config['notify_email'] ?? '',
            'notify_message' => $form_config['notify_message'] ?? '',
            'created_at' => current_time('mysql')
        ));

        return array(
            'form_id' => $wpdb->insert_id,
            'form_name' => $form_config['name'],
            'table_id' => $target_table['table_id']
        );
    }

    /**
     * Install template view
     */
    private static function install_template_view($view_config, $installed_tables) {
        global $wpdb;

        $views_table = $wpdb->prefix . 'dab_views';

        // Find the target table
        $target_table = null;
        foreach ($installed_tables as $table) {
            if ($table['table_slug'] === $view_config['table_slug'] ||
                str_ends_with($table['table_slug'], '_' . $view_config['table_slug'])) {
                $target_table = $table;
                break;
            }
        }

        if (!$target_table) {
            return new WP_Error('table_not_found', sprintf(__('Target table %s not found', 'db-app-builder'), $view_config['table_slug']));
        }

        // Insert view - now using both column sets for compatibility
        $wpdb->insert($views_table, array(
            'view_name' => $view_config['name'],
            'table_id' => $target_table['table_id'],
            'selected_fields' => maybe_serialize($view_config['columns'] ?? array()),
            'filter_conditions' => maybe_serialize($view_config['filters'] ?? array()),
            'columns' => json_encode($view_config['columns'] ?? array()),
            'filters' => json_encode($view_config['filters'] ?? array()),
            'sort_order' => $view_config['sort_order'] ?? '',
            'is_public' => $view_config['is_public'] ?? 0,
            'created_at' => current_time('mysql')
        ));

        return array(
            'view_id' => $wpdb->insert_id,
            'view_name' => $view_config['name'],
            'table_id' => $target_table['table_id']
        );
    }

    /**
     * Get template preview
     */
    public static function get_template_preview() {
        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Template Preview: Request received');
            error_log('DAB Template Preview: POST data: ' . print_r($_POST, true));
        }

        // Check if required POST data exists
        if (!isset($_POST['nonce'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Preview: Nonce not provided');
            }
            wp_send_json_error(__('Security nonce not provided', 'db-app-builder'));
        }

        if (!wp_verify_nonce($_POST['nonce'], 'dab_templates_nonce')) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Preview: Nonce verification failed');
            }
            wp_send_json_error(__('Security check failed', 'db-app-builder'));
        }

        // Check required parameters
        if (!isset($_POST['template_id'])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Preview: Template ID not provided');
            }
            wp_send_json_error(__('Template ID not provided', 'db-app-builder'));
        }

        $template_id = intval($_POST['template_id']);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Template Preview: Template ID: ' . $template_id);
        }

        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_app_templates';
        $template = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $templates_table WHERE id = %d AND is_active = 1",
            $template_id
        ));

        if (!$template) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('DAB Template Preview: Template not found for ID: ' . $template_id);
            }
            wp_send_json_error(__('Template not found', 'db-app-builder'));
        }

        $template_config = json_decode($template->template_config, true);

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Template Preview: Template found: ' . $template->name);
            error_log('DAB Template Preview: Config valid: ' . ($template_config ? 'Yes' : 'No'));
        }

        wp_send_json_success(array(
            'template' => $template,
            'config' => $template_config,
            'preview_html' => self::generate_preview_html($template, $template_config)
        ));
    }

    /**
     * Generate preview HTML
     */
    private static function generate_preview_html($template, $config) {
        ob_start();
        ?>
        <div class="dab-template-preview">
            <h3><?php echo esc_html($template->name); ?></h3>
            <p><?php echo esc_html($template->description); ?></p>

            <?php if (is_array($config) && isset($config['tables']) && is_array($config['tables'])): ?>
                <h4><?php _e('Tables', 'db-app-builder'); ?></h4>
                <ul class="dab-preview-list">
                    <?php foreach ($config['tables'] as $table): ?>
                        <?php if (is_array($table) && isset($table['label'])): ?>
                            <li>
                                <strong><?php echo esc_html($table['label']); ?></strong>
                                <?php if (isset($table['fields']) && is_array($table['fields'])): ?>
                                    <span class="dab-field-count">(<?php echo count($table['fields']); ?> fields)</span>
                                <?php endif; ?>
                                <?php if (isset($table['description'])): ?>
                                    <br><small><?php echo esc_html($table['description']); ?></small>
                                <?php endif; ?>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>

            <?php if (is_array($config) && isset($config['forms']) && is_array($config['forms'])): ?>
                <h4><?php _e('Forms', 'db-app-builder'); ?></h4>
                <ul class="dab-preview-list">
                    <?php foreach ($config['forms'] as $form): ?>
                        <?php if (is_array($form) && isset($form['name'])): ?>
                            <li><?php echo esc_html($form['name']); ?></li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>

            <?php if (is_array($config) && isset($config['views']) && is_array($config['views'])): ?>
                <h4><?php _e('Views', 'db-app-builder'); ?></h4>
                <ul class="dab-preview-list">
                    <?php foreach ($config['views'] as $view): ?>
                        <?php if (is_array($view) && isset($view['name'])): ?>
                            <li><?php echo esc_html($view['name']); ?></li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>

            <?php if (!is_array($config) || empty($config)): ?>
                <div class="dab-preview-error">
                    <p><em><?php _e('Template configuration is not available for preview.', 'db-app-builder'); ?></em></p>
                </div>
            <?php endif; ?>

            <div class="dab-template-info">
                <h4><?php _e('Template Information', 'db-app-builder'); ?></h4>
                <ul class="dab-preview-list">
                    <li><strong><?php _e('Category:', 'db-app-builder'); ?></strong> <?php echo esc_html(ucwords(str_replace('_', ' ', $template->category))); ?></li>
                    <li><strong><?php _e('Version:', 'db-app-builder'); ?></strong> <?php echo esc_html($template->version); ?></li>
                    <li><strong><?php _e('Install Count:', 'db-app-builder'); ?></strong> <?php echo intval($template->install_count); ?></li>
                </ul>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Search templates
     */
    public static function search_templates() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_templates_nonce')) {
            wp_send_json_error(__('Security check failed', 'db-app-builder'));
        }

        $search_term = sanitize_text_field($_POST['search'] ?? '');
        $category = sanitize_text_field($_POST['category'] ?? '');

        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_app_templates';

        // Ensure templates are loaded
        self::ensure_templates_loaded();

        $where_conditions = array('is_active = 1');
        $where_values = array();

        if (!empty($search_term)) {
            $where_conditions[] = '(name LIKE %s OR description LIKE %s)';
            $where_values[] = '%' . $wpdb->esc_like($search_term) . '%';
            $where_values[] = '%' . $wpdb->esc_like($search_term) . '%';
        }

        if (!empty($category) && $category !== 'all') {
            $where_conditions[] = 'category = %s';
            $where_values[] = $category;
        }

        $where_clause = implode(' AND ', $where_conditions);
        $query = "SELECT * FROM $templates_table WHERE $where_clause ORDER BY install_count DESC, name ASC";

        if (!empty($where_values)) {
            $templates = $wpdb->get_results($wpdb->prepare($query, $where_values));
        } else {
            $templates = $wpdb->get_results($query);
        }

        // Debug information
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Templates Search: Found ' . count($templates) . ' templates');
            error_log('DAB Templates Search SQL: ' . $query);
            if (!empty($where_values)) {
                error_log('DAB Templates Search Values: ' . print_r($where_values, true));
            }
        }

        wp_send_json_success(array('templates' => $templates));
    }

    /**
     * Delete template installation
     */
    public static function delete_template_installation() {
        if (!wp_verify_nonce($_POST['nonce'], 'dab_templates_nonce')) {
            wp_send_json_error(__('Security check failed', 'db-app-builder'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'db-app-builder'));
        }

        $installation_id = intval($_POST['installation_id']);

        global $wpdb;
        $installations_table = $wpdb->prefix . 'dab_template_installations';

        // Get installation data
        $installation = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $installations_table WHERE id = %d",
            $installation_id
        ));

        if (!$installation) {
            wp_send_json_error(__('Installation not found', 'db-app-builder'));
        }

        // Delete related data (tables, forms, views)
        $installation_data = json_decode($installation->installation_data, true);

        if (isset($installation_data['tables'])) {
            foreach ($installation_data['tables'] as $table) {
                // Delete data table
                $wpdb->query($wpdb->prepare("DROP TABLE IF EXISTS %s", $wpdb->prefix . 'dab_' . $table['table_slug']));

                // Delete table record
                $wpdb->delete($wpdb->prefix . 'dab_tables', array('id' => $table['table_id']));

                // Delete fields
                $wpdb->delete($wpdb->prefix . 'dab_fields', array('table_id' => $table['table_id']));
            }
        }

        if (isset($installation_data['forms'])) {
            foreach ($installation_data['forms'] as $form) {
                $wpdb->delete($wpdb->prefix . 'dab_forms', array('id' => $form['form_id']));
            }
        }

        if (isset($installation_data['views'])) {
            foreach ($installation_data['views'] as $view) {
                $wpdb->delete($wpdb->prefix . 'dab_views', array('id' => $view['view_id']));
            }
        }

        // Delete installation record
        $wpdb->delete($installations_table, array('id' => $installation_id));

        wp_send_json_success(array('message' => __('Template installation deleted successfully', 'db-app-builder')));
    }

    /**
     * Insert default templates
     */
    private static function insert_default_templates() {
        global $wpdb;
        $templates_table = $wpdb->prefix . 'dab_app_templates';

        // Clear existing system templates to avoid duplicates
        $wpdb->query("DELETE FROM $templates_table WHERE is_system = 1");

        $default_templates = array(
            // Customer & Sales Management
            array(
                'template_key' => 'crm_basic',
                'name' => __('Customer Relationship Management (CRM)', 'db-app-builder'),
                'description' => __('Complete CRM system with customers, contacts, deals, and interactions tracking.', 'db-app-builder'),
                'category' => 'customer_sales',
                'subcategory' => 'crm',
                'icon' => 'dashicons-groups',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'customers',
                            'label' => 'Customers',
                            'description' => 'Customer information and details',
                            'fields' => array(
                                array('slug' => 'company_name', 'label' => 'Company Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'contact_person', 'label' => 'Contact Person', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 0),
                                array('slug' => 'address', 'label' => 'Address', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'industry', 'label' => 'Industry', 'type' => 'select', 'required' => 0, 'options' => array('Technology', 'Healthcare', 'Finance', 'Manufacturing', 'Retail', 'Other')),
                                array('slug' => 'customer_status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Lead', 'Prospect', 'Customer', 'Inactive')),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0)
                            )
                        ),
                        array(
                            'slug' => 'deals',
                            'label' => 'Deals',
                            'description' => 'Sales opportunities and deals',
                            'fields' => array(
                                array('slug' => 'deal_name', 'label' => 'Deal Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'customer_id', 'label' => 'Customer', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'deal_value', 'label' => 'Deal Value', 'type' => 'number', 'required' => 1),
                                array('slug' => 'deal_stage', 'label' => 'Stage', 'type' => 'select', 'required' => 1, 'options' => array('Prospecting', 'Qualification', 'Proposal', 'Negotiation', 'Closed Won', 'Closed Lost')),
                                array('slug' => 'probability', 'label' => 'Probability (%)', 'type' => 'number', 'required' => 0),
                                array('slug' => 'expected_close_date', 'label' => 'Expected Close Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Customer',
                            'table_slug' => 'customers',
                            'fields' => array('company_name', 'contact_person', 'email', 'phone', 'address', 'industry', 'customer_status', 'notes')
                        ),
                        array(
                            'name' => 'Add Deal',
                            'table_slug' => 'deals',
                            'fields' => array('deal_name', 'customer_id', 'deal_value', 'deal_stage', 'probability', 'expected_close_date', 'description')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'All Customers',
                            'table_slug' => 'customers',
                            'columns' => array('company_name', 'contact_person', 'email', 'customer_status'),
                            'sort_order' => 'company_name ASC'
                        ),
                        array(
                            'name' => 'Active Deals',
                            'table_slug' => 'deals',
                            'columns' => array('deal_name', 'deal_value', 'deal_stage', 'probability', 'expected_close_date'),
                            'filters' => array(array('field' => 'deal_stage', 'operator' => 'NOT IN', 'value' => 'Closed Won,Closed Lost')),
                            'sort_order' => 'expected_close_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Project Management
            array(
                'template_key' => 'project_management',
                'name' => __('Project Management System', 'db-app-builder'),
                'description' => __('Complete project management with projects, tasks, milestones, and team collaboration.', 'db-app-builder'),
                'category' => 'project_task',
                'subcategory' => 'project_management',
                'icon' => 'dashicons-calendar-alt',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'projects',
                            'label' => 'Projects',
                            'description' => 'Project information and tracking',
                            'fields' => array(
                                array('slug' => 'project_name', 'label' => 'Project Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'project_manager', 'label' => 'Project Manager', 'type' => 'text', 'required' => 1),
                                array('slug' => 'start_date', 'label' => 'Start Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'end_date', 'label' => 'End Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'budget', 'label' => 'Budget', 'type' => 'number', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Planning', 'In Progress', 'On Hold', 'Completed', 'Cancelled')),
                                array('slug' => 'priority', 'label' => 'Priority', 'type' => 'select', 'required' => 1, 'options' => array('Low', 'Medium', 'High', 'Critical'))
                            )
                        ),
                        array(
                            'slug' => 'tasks',
                            'label' => 'Tasks',
                            'description' => 'Project tasks and assignments',
                            'fields' => array(
                                array('slug' => 'task_name', 'label' => 'Task Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'project_id', 'label' => 'Project', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'assigned_to', 'label' => 'Assigned To', 'type' => 'text', 'required' => 0),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'due_date', 'label' => 'Due Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Not Started', 'In Progress', 'Completed', 'Blocked')),
                                array('slug' => 'priority', 'label' => 'Priority', 'type' => 'select', 'required' => 1, 'options' => array('Low', 'Medium', 'High', 'Critical')),
                                array('slug' => 'estimated_hours', 'label' => 'Estimated Hours', 'type' => 'number', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Create Project',
                            'table_slug' => 'projects',
                            'fields' => array('project_name', 'description', 'project_manager', 'start_date', 'end_date', 'budget', 'status', 'priority')
                        ),
                        array(
                            'name' => 'Add Task',
                            'table_slug' => 'tasks',
                            'fields' => array('task_name', 'project_id', 'assigned_to', 'description', 'due_date', 'status', 'priority', 'estimated_hours')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Active Projects',
                            'table_slug' => 'projects',
                            'columns' => array('project_name', 'project_manager', 'start_date', 'end_date', 'status', 'priority'),
                            'filters' => array(array('field' => 'status', 'operator' => 'NOT IN', 'value' => 'Completed,Cancelled')),
                            'sort_order' => 'start_date ASC'
                        ),
                        array(
                            'name' => 'My Tasks',
                            'table_slug' => 'tasks',
                            'columns' => array('task_name', 'due_date', 'status', 'priority'),
                            'filters' => array(array('field' => 'status', 'operator' => '!=', 'value' => 'Completed')),
                            'sort_order' => 'due_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Employee Management
            array(
                'template_key' => 'employee_management',
                'name' => __('Employee Management System', 'db-app-builder'),
                'description' => __('Comprehensive HR system for managing employees, departments, and basic HR functions.', 'db-app-builder'),
                'category' => 'human_resources',
                'subcategory' => 'employee_management',
                'icon' => 'dashicons-admin-users',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'employees',
                            'label' => 'Employees',
                            'description' => 'Employee information and records',
                            'fields' => array(
                                array('slug' => 'employee_id', 'label' => 'Employee ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 0),
                                array('slug' => 'department', 'label' => 'Department', 'type' => 'select', 'required' => 1, 'options' => array('HR', 'IT', 'Finance', 'Marketing', 'Sales', 'Operations')),
                                array('slug' => 'position', 'label' => 'Position', 'type' => 'text', 'required' => 1),
                                array('slug' => 'hire_date', 'label' => 'Hire Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'salary', 'label' => 'Salary', 'type' => 'number', 'required' => 0),
                                array('slug' => 'employment_status', 'label' => 'Employment Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Terminated', 'On Leave'))
                            )
                        ),
                        array(
                            'slug' => 'leave_requests',
                            'label' => 'Leave Requests',
                            'description' => 'Employee leave and vacation requests',
                            'fields' => array(
                                array('slug' => 'employee_id', 'label' => 'Employee', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'leave_type', 'label' => 'Leave Type', 'type' => 'select', 'required' => 1, 'options' => array('Vacation', 'Sick Leave', 'Personal Leave', 'Maternity/Paternity', 'Emergency')),
                                array('slug' => 'start_date', 'label' => 'Start Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'end_date', 'label' => 'End Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'days_requested', 'label' => 'Days Requested', 'type' => 'number', 'required' => 1),
                                array('slug' => 'reason', 'label' => 'Reason', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Pending', 'Approved', 'Rejected')),
                                array('slug' => 'approved_by', 'label' => 'Approved By', 'type' => 'text', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Employee',
                            'table_slug' => 'employees',
                            'fields' => array('employee_id', 'first_name', 'last_name', 'email', 'phone', 'department', 'position', 'hire_date', 'salary', 'employment_status')
                        ),
                        array(
                            'name' => 'Leave Request',
                            'table_slug' => 'leave_requests',
                            'fields' => array('employee_id', 'leave_type', 'start_date', 'end_date', 'days_requested', 'reason')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'All Employees',
                            'table_slug' => 'employees',
                            'columns' => array('employee_id', 'first_name', 'last_name', 'department', 'position', 'employment_status'),
                            'sort_order' => 'last_name ASC'
                        ),
                        array(
                            'name' => 'Pending Leave Requests',
                            'table_slug' => 'leave_requests',
                            'columns' => array('employee_id', 'leave_type', 'start_date', 'end_date', 'days_requested', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Pending')),
                            'sort_order' => 'start_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Inventory Management
            array(
                'template_key' => 'inventory_management',
                'name' => __('Inventory Management System', 'db-app-builder'),
                'description' => __('Complete inventory tracking with products, stock levels, suppliers, and purchase orders.', 'db-app-builder'),
                'category' => 'inventory_operations',
                'subcategory' => 'inventory',
                'icon' => 'dashicons-archive',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'products',
                            'label' => 'Products',
                            'description' => 'Product catalog and information',
                            'fields' => array(
                                array('slug' => 'product_code', 'label' => 'Product Code', 'type' => 'text', 'required' => 1),
                                array('slug' => 'product_name', 'label' => 'Product Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'category', 'label' => 'Category', 'type' => 'select', 'required' => 1, 'options' => array('Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports', 'Other')),
                                array('slug' => 'unit_price', 'label' => 'Unit Price', 'type' => 'number', 'required' => 1),
                                array('slug' => 'cost_price', 'label' => 'Cost Price', 'type' => 'number', 'required' => 0),
                                array('slug' => 'current_stock', 'label' => 'Current Stock', 'type' => 'number', 'required' => 1),
                                array('slug' => 'minimum_stock', 'label' => 'Minimum Stock Level', 'type' => 'number', 'required' => 1),
                                array('slug' => 'supplier', 'label' => 'Supplier', 'type' => 'text', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Discontinued'))
                            )
                        ),
                        array(
                            'slug' => 'stock_movements',
                            'label' => 'Stock Movements',
                            'description' => 'Track stock in and out movements',
                            'fields' => array(
                                array('slug' => 'product_id', 'label' => 'Product', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'movement_type', 'label' => 'Movement Type', 'type' => 'select', 'required' => 1, 'options' => array('Stock In', 'Stock Out', 'Adjustment', 'Transfer')),
                                array('slug' => 'quantity', 'label' => 'Quantity', 'type' => 'number', 'required' => 1),
                                array('slug' => 'reference', 'label' => 'Reference', 'type' => 'text', 'required' => 0),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'movement_date', 'label' => 'Movement Date', 'type' => 'date', 'required' => 1)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Product',
                            'table_slug' => 'products',
                            'fields' => array('product_code', 'product_name', 'description', 'category', 'unit_price', 'cost_price', 'current_stock', 'minimum_stock', 'supplier', 'status')
                        ),
                        array(
                            'name' => 'Stock Movement',
                            'table_slug' => 'stock_movements',
                            'fields' => array('product_id', 'movement_type', 'quantity', 'reference', 'notes', 'movement_date')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'All Products',
                            'table_slug' => 'products',
                            'columns' => array('product_code', 'product_name', 'category', 'current_stock', 'minimum_stock', 'status'),
                            'sort_order' => 'product_name ASC'
                        ),
                        array(
                            'name' => 'Low Stock Alert',
                            'table_slug' => 'products',
                            'columns' => array('product_code', 'product_name', 'current_stock', 'minimum_stock'),
                            'filters' => array(array('field' => 'current_stock', 'operator' => '<=', 'value' => 'minimum_stock')),
                            'sort_order' => 'current_stock ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Event Management
            array(
                'template_key' => 'event_management',
                'name' => __('Event Management System', 'db-app-builder'),
                'description' => __('Comprehensive event planning with events, attendees, venues, and registration management.', 'db-app-builder'),
                'category' => 'event_hospitality',
                'subcategory' => 'event_planning',
                'icon' => 'dashicons-calendar',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'events',
                            'label' => 'Events',
                            'description' => 'Event information and details',
                            'fields' => array(
                                array('slug' => 'event_name', 'label' => 'Event Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'event_type', 'label' => 'Event Type', 'type' => 'select', 'required' => 1, 'options' => array('Conference', 'Workshop', 'Seminar', 'Meeting', 'Social', 'Training', 'Other')),
                                array('slug' => 'start_date', 'label' => 'Start Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'end_date', 'label' => 'End Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'start_time', 'label' => 'Start Time', 'type' => 'time', 'required' => 1),
                                array('slug' => 'end_time', 'label' => 'End Time', 'type' => 'time', 'required' => 1),
                                array('slug' => 'venue', 'label' => 'Venue', 'type' => 'text', 'required' => 1),
                                array('slug' => 'max_attendees', 'label' => 'Maximum Attendees', 'type' => 'number', 'required' => 0),
                                array('slug' => 'registration_fee', 'label' => 'Registration Fee', 'type' => 'number', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Planning', 'Open for Registration', 'Registration Closed', 'In Progress', 'Completed', 'Cancelled'))
                            )
                        ),
                        array(
                            'slug' => 'registrations',
                            'label' => 'Event Registrations',
                            'description' => 'Event attendee registrations',
                            'fields' => array(
                                array('slug' => 'event_id', 'label' => 'Event', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'attendee_name', 'label' => 'Attendee Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 0),
                                array('slug' => 'organization', 'label' => 'Organization', 'type' => 'text', 'required' => 0),
                                array('slug' => 'dietary_requirements', 'label' => 'Dietary Requirements', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'registration_status', 'label' => 'Registration Status', 'type' => 'select', 'required' => 1, 'options' => array('Registered', 'Confirmed', 'Attended', 'No Show', 'Cancelled')),
                                array('slug' => 'payment_status', 'label' => 'Payment Status', 'type' => 'select', 'required' => 1, 'options' => array('Pending', 'Paid', 'Refunded', 'Waived'))
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Create Event',
                            'table_slug' => 'events',
                            'fields' => array('event_name', 'description', 'event_type', 'start_date', 'end_date', 'start_time', 'end_time', 'venue', 'max_attendees', 'registration_fee', 'status')
                        ),
                        array(
                            'name' => 'Event Registration',
                            'table_slug' => 'registrations',
                            'fields' => array('event_id', 'attendee_name', 'email', 'phone', 'organization', 'dietary_requirements')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Upcoming Events',
                            'table_slug' => 'events',
                            'columns' => array('event_name', 'event_type', 'start_date', 'venue', 'status'),
                            'filters' => array(array('field' => 'start_date', 'operator' => '>=', 'value' => 'TODAY()')),
                            'sort_order' => 'start_date ASC'
                        ),
                        array(
                            'name' => 'Event Registrations',
                            'table_slug' => 'registrations',
                            'columns' => array('event_id', 'attendee_name', 'email', 'registration_status', 'payment_status'),
                            'sort_order' => 'created_at DESC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Expense Management
            array(
                'template_key' => 'expense_management',
                'name' => __('Expense Management System', 'db-app-builder'),
                'description' => __('Track and manage business expenses with categories, approvals, and reporting.', 'db-app-builder'),
                'category' => 'financial',
                'subcategory' => 'expense_management',
                'icon' => 'dashicons-money-alt',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'expenses',
                            'label' => 'Expenses',
                            'description' => 'Business expense records',
                            'fields' => array(
                                array('slug' => 'expense_title', 'label' => 'Expense Title', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'amount', 'label' => 'Amount', 'type' => 'number', 'required' => 1),
                                array('slug' => 'currency', 'label' => 'Currency', 'type' => 'select', 'required' => 1, 'options' => array('USD', 'EUR', 'GBP', 'CAD', 'AUD')),
                                array('slug' => 'category', 'label' => 'Category', 'type' => 'select', 'required' => 1, 'options' => array('Travel', 'Meals', 'Office Supplies', 'Software', 'Marketing', 'Training', 'Other')),
                                array('slug' => 'expense_date', 'label' => 'Expense Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'vendor', 'label' => 'Vendor/Merchant', 'type' => 'text', 'required' => 0),
                                array('slug' => 'payment_method', 'label' => 'Payment Method', 'type' => 'select', 'required' => 1, 'options' => array('Cash', 'Credit Card', 'Debit Card', 'Bank Transfer', 'Check')),
                                array('slug' => 'receipt_number', 'label' => 'Receipt Number', 'type' => 'text', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Draft', 'Submitted', 'Approved', 'Rejected', 'Reimbursed')),
                                array('slug' => 'submitted_by', 'label' => 'Submitted By', 'type' => 'text', 'required' => 1)
                            )
                        ),
                        array(
                            'slug' => 'expense_approvals',
                            'label' => 'Expense Approvals',
                            'description' => 'Expense approval workflow',
                            'fields' => array(
                                array('slug' => 'expense_id', 'label' => 'Expense', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'approver', 'label' => 'Approver', 'type' => 'text', 'required' => 1),
                                array('slug' => 'approval_status', 'label' => 'Approval Status', 'type' => 'select', 'required' => 1, 'options' => array('Pending', 'Approved', 'Rejected')),
                                array('slug' => 'approval_date', 'label' => 'Approval Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'comments', 'label' => 'Comments', 'type' => 'textarea', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Submit Expense',
                            'table_slug' => 'expenses',
                            'fields' => array('expense_title', 'description', 'amount', 'currency', 'category', 'expense_date', 'vendor', 'payment_method', 'receipt_number', 'submitted_by')
                        ),
                        array(
                            'name' => 'Expense Approval',
                            'table_slug' => 'expense_approvals',
                            'fields' => array('expense_id', 'approver', 'approval_status', 'approval_date', 'comments')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'All Expenses',
                            'table_slug' => 'expenses',
                            'columns' => array('expense_title', 'amount', 'category', 'expense_date', 'status', 'submitted_by'),
                            'sort_order' => 'expense_date DESC'
                        ),
                        array(
                            'name' => 'Pending Approvals',
                            'table_slug' => 'expenses',
                            'columns' => array('expense_title', 'amount', 'category', 'submitted_by', 'expense_date'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Submitted')),
                            'sort_order' => 'expense_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Legal Case Management
            array(
                'template_key' => 'legal_case_management',
                'name' => __('Legal Case Management System', 'db-app-builder'),
                'description' => __('Comprehensive legal practice management with cases, clients, documents, and billing tracking.', 'db-app-builder'),
                'category' => 'legal_compliance',
                'subcategory' => 'case_management',
                'icon' => 'dashicons-businessman',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'legal_clients',
                            'label' => 'Legal Clients',
                            'description' => 'Client information and contact details',
                            'fields' => array(
                                array('slug' => 'client_id', 'label' => 'Client ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'client_type', 'label' => 'Client Type', 'type' => 'select', 'required' => 1, 'options' => array('Individual', 'Corporation', 'Partnership', 'Non-Profit', 'Government')),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'company_name', 'label' => 'Company Name', 'type' => 'text', 'required' => 0),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'address', 'label' => 'Address', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'billing_address', 'label' => 'Billing Address', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'retainer_amount', 'label' => 'Retainer Amount', 'type' => 'number', 'required' => 0),
                                array('slug' => 'hourly_rate', 'label' => 'Hourly Rate', 'type' => 'number', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Prospective', 'Former'))
                            )
                        ),
                        array(
                            'slug' => 'legal_cases',
                            'label' => 'Legal Cases',
                            'description' => 'Case management and tracking',
                            'fields' => array(
                                array('slug' => 'case_number', 'label' => 'Case Number', 'type' => 'text', 'required' => 1),
                                array('slug' => 'case_title', 'label' => 'Case Title', 'type' => 'text', 'required' => 1),
                                array('slug' => 'client_id', 'label' => 'Client', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'case_type', 'label' => 'Case Type', 'type' => 'select', 'required' => 1, 'options' => array('Civil Litigation', 'Criminal Defense', 'Corporate Law', 'Family Law', 'Real Estate', 'Personal Injury', 'Immigration', 'Bankruptcy', 'Intellectual Property')),
                                array('slug' => 'court_name', 'label' => 'Court Name', 'type' => 'text', 'required' => 0),
                                array('slug' => 'judge_name', 'label' => 'Judge Name', 'type' => 'text', 'required' => 0),
                                array('slug' => 'opposing_counsel', 'label' => 'Opposing Counsel', 'type' => 'text', 'required' => 0),
                                array('slug' => 'case_description', 'label' => 'Case Description', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'start_date', 'label' => 'Start Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'expected_end_date', 'label' => 'Expected End Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'priority', 'label' => 'Priority', 'type' => 'select', 'required' => 1, 'options' => array('Low', 'Medium', 'High', 'Critical')),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Open', 'In Progress', 'On Hold', 'Settled', 'Won', 'Lost', 'Dismissed', 'Closed'))
                            )
                        ),
                        array(
                            'slug' => 'legal_documents',
                            'label' => 'Legal Documents',
                            'description' => 'Document management and tracking',
                            'fields' => array(
                                array('slug' => 'document_id', 'label' => 'Document ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'case_id', 'label' => 'Case', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'document_name', 'label' => 'Document Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'document_type', 'label' => 'Document Type', 'type' => 'select', 'required' => 1, 'options' => array('Contract', 'Motion', 'Brief', 'Pleading', 'Discovery', 'Evidence', 'Correspondence', 'Court Order', 'Settlement Agreement')),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'created_date', 'label' => 'Created Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'due_date', 'label' => 'Due Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'filed_date', 'label' => 'Filed Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'created_by', 'label' => 'Created By', 'type' => 'text', 'required' => 1),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Draft', 'Review', 'Approved', 'Filed', 'Served', 'Archived'))
                            )
                        ),
                        array(
                            'slug' => 'legal_billing',
                            'label' => 'Legal Billing',
                            'description' => 'Time tracking and billing management',
                            'fields' => array(
                                array('slug' => 'billing_id', 'label' => 'Billing ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'case_id', 'label' => 'Case', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'client_id', 'label' => 'Client', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'work_date', 'label' => 'Work Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'attorney_name', 'label' => 'Attorney Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'work_description', 'label' => 'Work Description', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'hours_worked', 'label' => 'Hours Worked', 'type' => 'number', 'required' => 1),
                                array('slug' => 'hourly_rate', 'label' => 'Hourly Rate', 'type' => 'number', 'required' => 1),
                                array('slug' => 'total_amount', 'label' => 'Total Amount', 'type' => 'number', 'required' => 1),
                                array('slug' => 'billable', 'label' => 'Billable', 'type' => 'select', 'required' => 1, 'options' => array('Yes', 'No')),
                                array('slug' => 'billed', 'label' => 'Billed', 'type' => 'select', 'required' => 1, 'options' => array('Yes', 'No')),
                                array('slug' => 'invoice_number', 'label' => 'Invoice Number', 'type' => 'text', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Client',
                            'table_slug' => 'legal_clients',
                            'fields' => array('client_id', 'client_type', 'first_name', 'last_name', 'company_name', 'email', 'phone', 'address', 'billing_address', 'retainer_amount', 'hourly_rate', 'status')
                        ),
                        array(
                            'name' => 'Create Case',
                            'table_slug' => 'legal_cases',
                            'fields' => array('case_number', 'case_title', 'client_id', 'case_type', 'court_name', 'judge_name', 'opposing_counsel', 'case_description', 'start_date', 'expected_end_date', 'priority', 'status')
                        ),
                        array(
                            'name' => 'Add Document',
                            'table_slug' => 'legal_documents',
                            'fields' => array('document_id', 'case_id', 'document_name', 'document_type', 'description', 'created_date', 'due_date', 'created_by', 'status')
                        ),
                        array(
                            'name' => 'Time Entry',
                            'table_slug' => 'legal_billing',
                            'fields' => array('billing_id', 'case_id', 'client_id', 'work_date', 'attorney_name', 'work_description', 'hours_worked', 'hourly_rate', 'total_amount', 'billable')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Active Cases',
                            'table_slug' => 'legal_cases',
                            'columns' => array('case_number', 'case_title', 'client_id', 'case_type', 'priority', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => 'NOT IN', 'value' => 'Closed,Settled,Dismissed')),
                            'sort_order' => 'priority DESC, start_date ASC'
                        ),
                        array(
                            'name' => 'Unbilled Time',
                            'table_slug' => 'legal_billing',
                            'columns' => array('billing_id', 'case_id', 'work_date', 'attorney_name', 'hours_worked', 'total_amount'),
                            'filters' => array(array('field' => 'billable', 'operator' => '=', 'value' => 'Yes'), array('field' => 'billed', 'operator' => '=', 'value' => 'No')),
                            'sort_order' => 'work_date DESC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Gym/Fitness Management
            array(
                'template_key' => 'gym_fitness_management',
                'name' => __('Gym & Fitness Management System', 'db-app-builder'),
                'description' => __('Complete fitness center management with members, classes, trainers, and equipment tracking.', 'db-app-builder'),
                'category' => 'specialized_industries',
                'subcategory' => 'fitness',
                'icon' => 'dashicons-universal-access',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'gym_members',
                            'label' => 'Gym Members',
                            'description' => 'Member information and membership details',
                            'fields' => array(
                                array('slug' => 'member_id', 'label' => 'Member ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'date_of_birth', 'label' => 'Date of Birth', 'type' => 'date', 'required' => 1),
                                array('slug' => 'gender', 'label' => 'Gender', 'type' => 'select', 'required' => 1, 'options' => array('Male', 'Female', 'Other')),
                                array('slug' => 'emergency_contact', 'label' => 'Emergency Contact', 'type' => 'text', 'required' => 1),
                                array('slug' => 'emergency_phone', 'label' => 'Emergency Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'membership_type', 'label' => 'Membership Type', 'type' => 'select', 'required' => 1, 'options' => array('Basic', 'Premium', 'VIP', 'Student', 'Senior', 'Family')),
                                array('slug' => 'join_date', 'label' => 'Join Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'membership_expiry', 'label' => 'Membership Expiry', 'type' => 'date', 'required' => 1),
                                array('slug' => 'monthly_fee', 'label' => 'Monthly Fee', 'type' => 'number', 'required' => 1),
                                array('slug' => 'medical_conditions', 'label' => 'Medical Conditions', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'fitness_goals', 'label' => 'Fitness Goals', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Suspended', 'Expired'))
                            )
                        ),
                        array(
                            'slug' => 'fitness_classes',
                            'label' => 'Fitness Classes',
                            'description' => 'Group fitness classes and schedules',
                            'fields' => array(
                                array('slug' => 'class_id', 'label' => 'Class ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'class_name', 'label' => 'Class Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'class_type', 'label' => 'Class Type', 'type' => 'select', 'required' => 1, 'options' => array('Cardio', 'Strength Training', 'Yoga', 'Pilates', 'Zumba', 'Spinning', 'CrossFit', 'Martial Arts', 'Swimming')),
                                array('slug' => 'instructor_name', 'label' => 'Instructor Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'class_date', 'label' => 'Class Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'start_time', 'label' => 'Start Time', 'type' => 'time', 'required' => 1),
                                array('slug' => 'end_time', 'label' => 'End Time', 'type' => 'time', 'required' => 1),
                                array('slug' => 'duration_minutes', 'label' => 'Duration (minutes)', 'type' => 'number', 'required' => 1),
                                array('slug' => 'max_participants', 'label' => 'Max Participants', 'type' => 'number', 'required' => 1),
                                array('slug' => 'current_participants', 'label' => 'Current Participants', 'type' => 'number', 'required' => 0),
                                array('slug' => 'room_location', 'label' => 'Room/Location', 'type' => 'text', 'required' => 1),
                                array('slug' => 'difficulty_level', 'label' => 'Difficulty Level', 'type' => 'select', 'required' => 1, 'options' => array('Beginner', 'Intermediate', 'Advanced', 'All Levels')),
                                array('slug' => 'class_fee', 'label' => 'Class Fee', 'type' => 'number', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Scheduled', 'In Progress', 'Completed', 'Cancelled'))
                            )
                        ),
                        array(
                            'slug' => 'class_bookings',
                            'label' => 'Class Bookings',
                            'description' => 'Member class reservations and attendance',
                            'fields' => array(
                                array('slug' => 'booking_id', 'label' => 'Booking ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'member_id', 'label' => 'Member', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'class_id', 'label' => 'Class', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'booking_date', 'label' => 'Booking Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'booking_time', 'label' => 'Booking Time', 'type' => 'time', 'required' => 1),
                                array('slug' => 'payment_status', 'label' => 'Payment Status', 'type' => 'select', 'required' => 1, 'options' => array('Paid', 'Pending', 'Waived', 'Refunded')),
                                array('slug' => 'attendance_status', 'label' => 'Attendance Status', 'type' => 'select', 'required' => 1, 'options' => array('Booked', 'Attended', 'No Show', 'Cancelled')),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0)
                            )
                        ),
                        array(
                            'slug' => 'gym_equipment',
                            'label' => 'Gym Equipment',
                            'description' => 'Equipment inventory and maintenance tracking',
                            'fields' => array(
                                array('slug' => 'equipment_id', 'label' => 'Equipment ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'equipment_name', 'label' => 'Equipment Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'equipment_type', 'label' => 'Equipment Type', 'type' => 'select', 'required' => 1, 'options' => array('Cardio', 'Strength', 'Free Weights', 'Functional', 'Stretching', 'Other')),
                                array('slug' => 'brand', 'label' => 'Brand', 'type' => 'text', 'required' => 0),
                                array('slug' => 'model', 'label' => 'Model', 'type' => 'text', 'required' => 0),
                                array('slug' => 'serial_number', 'label' => 'Serial Number', 'type' => 'text', 'required' => 0),
                                array('slug' => 'purchase_date', 'label' => 'Purchase Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'purchase_cost', 'label' => 'Purchase Cost', 'type' => 'number', 'required' => 0),
                                array('slug' => 'location', 'label' => 'Location', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_maintenance', 'label' => 'Last Maintenance', 'type' => 'date', 'required' => 0),
                                array('slug' => 'next_maintenance', 'label' => 'Next Maintenance', 'type' => 'date', 'required' => 0),
                                array('slug' => 'warranty_expiry', 'label' => 'Warranty Expiry', 'type' => 'date', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Under Maintenance', 'Out of Order', 'Retired'))
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Member Registration',
                            'table_slug' => 'gym_members',
                            'fields' => array('member_id', 'first_name', 'last_name', 'email', 'phone', 'date_of_birth', 'gender', 'emergency_contact', 'emergency_phone', 'membership_type', 'join_date', 'membership_expiry', 'monthly_fee', 'medical_conditions', 'fitness_goals')
                        ),
                        array(
                            'name' => 'Schedule Class',
                            'table_slug' => 'fitness_classes',
                            'fields' => array('class_id', 'class_name', 'description', 'class_type', 'instructor_name', 'class_date', 'start_time', 'end_time', 'duration_minutes', 'max_participants', 'room_location', 'difficulty_level', 'class_fee')
                        ),
                        array(
                            'name' => 'Book Class',
                            'table_slug' => 'class_bookings',
                            'fields' => array('booking_id', 'member_id', 'class_id', 'booking_date', 'booking_time', 'payment_status')
                        ),
                        array(
                            'name' => 'Add Equipment',
                            'table_slug' => 'gym_equipment',
                            'fields' => array('equipment_id', 'equipment_name', 'equipment_type', 'brand', 'model', 'serial_number', 'purchase_date', 'purchase_cost', 'location', 'warranty_expiry')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Active Members',
                            'table_slug' => 'gym_members',
                            'columns' => array('member_id', 'first_name', 'last_name', 'membership_type', 'membership_expiry', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Active')),
                            'sort_order' => 'last_name ASC'
                        ),
                        array(
                            'name' => 'Today\'s Classes',
                            'table_slug' => 'fitness_classes',
                            'columns' => array('class_name', 'instructor_name', 'start_time', 'room_location', 'current_participants', 'max_participants'),
                            'filters' => array(array('field' => 'class_date', 'operator' => '=', 'value' => 'CURDATE()')),
                            'sort_order' => 'start_time ASC'
                        ),
                        array(
                            'name' => 'Equipment Maintenance Due',
                            'table_slug' => 'gym_equipment',
                            'columns' => array('equipment_id', 'equipment_name', 'location', 'next_maintenance', 'status'),
                            'filters' => array(array('field' => 'next_maintenance', 'operator' => '<=', 'value' => 'DATE_ADD(CURDATE(), INTERVAL 7 DAY)')),
                            'sort_order' => 'next_maintenance ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Library Management System
            array(
                'template_key' => 'library_management',
                'name' => __('Library Management System', 'db-app-builder'),
                'description' => __('Complete library operations with books, members, borrowing, and catalog management.', 'db-app-builder'),
                'category' => 'education_training',
                'subcategory' => 'library_management',
                'icon' => 'dashicons-book',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'books',
                            'label' => 'Books',
                            'description' => 'Library book catalog and inventory',
                            'fields' => array(
                                array('slug' => 'isbn', 'label' => 'ISBN', 'type' => 'text', 'required' => 1),
                                array('slug' => 'title', 'label' => 'Title', 'type' => 'text', 'required' => 1),
                                array('slug' => 'author', 'label' => 'Author', 'type' => 'text', 'required' => 1),
                                array('slug' => 'publisher', 'label' => 'Publisher', 'type' => 'text', 'required' => 0),
                                array('slug' => 'publication_year', 'label' => 'Publication Year', 'type' => 'number', 'required' => 0),
                                array('slug' => 'category', 'label' => 'Category', 'type' => 'select', 'required' => 1, 'options' => array('Fiction', 'Non-Fiction', 'Science', 'History', 'Biography', 'Children', 'Reference', 'Textbook', 'Other')),
                                array('slug' => 'genre', 'label' => 'Genre', 'type' => 'text', 'required' => 0),
                                array('slug' => 'language', 'label' => 'Language', 'type' => 'select', 'required' => 1, 'options' => array('English', 'Spanish', 'French', 'German', 'Chinese', 'Japanese', 'Other')),
                                array('slug' => 'pages', 'label' => 'Number of Pages', 'type' => 'number', 'required' => 0),
                                array('slug' => 'location', 'label' => 'Shelf Location', 'type' => 'text', 'required' => 1),
                                array('slug' => 'total_copies', 'label' => 'Total Copies', 'type' => 'number', 'required' => 1),
                                array('slug' => 'available_copies', 'label' => 'Available Copies', 'type' => 'number', 'required' => 1),
                                array('slug' => 'acquisition_date', 'label' => 'Acquisition Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'cost', 'label' => 'Cost', 'type' => 'number', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Available', 'Checked Out', 'Reserved', 'Lost', 'Damaged', 'Withdrawn'))
                            )
                        ),
                        array(
                            'slug' => 'library_members',
                            'label' => 'Library Members',
                            'description' => 'Library member information and records',
                            'fields' => array(
                                array('slug' => 'member_id', 'label' => 'Member ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'address', 'label' => 'Address', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'date_of_birth', 'label' => 'Date of Birth', 'type' => 'date', 'required' => 0),
                                array('slug' => 'membership_type', 'label' => 'Membership Type', 'type' => 'select', 'required' => 1, 'options' => array('Student', 'Faculty', 'Staff', 'Public', 'Senior', 'Child')),
                                array('slug' => 'registration_date', 'label' => 'Registration Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'expiry_date', 'label' => 'Membership Expiry', 'type' => 'date', 'required' => 1),
                                array('slug' => 'max_books', 'label' => 'Max Books Allowed', 'type' => 'number', 'required' => 1),
                                array('slug' => 'current_books', 'label' => 'Current Books Borrowed', 'type' => 'number', 'required' => 0),
                                array('slug' => 'fines_owed', 'label' => 'Fines Owed', 'type' => 'number', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Suspended', 'Expired'))
                            )
                        ),
                        array(
                            'slug' => 'book_loans',
                            'label' => 'Book Loans',
                            'description' => 'Book borrowing and return tracking',
                            'fields' => array(
                                array('slug' => 'loan_id', 'label' => 'Loan ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'member_id', 'label' => 'Member', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'book_isbn', 'label' => 'Book', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'checkout_date', 'label' => 'Checkout Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'due_date', 'label' => 'Due Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'return_date', 'label' => 'Return Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'renewal_count', 'label' => 'Renewal Count', 'type' => 'number', 'required' => 0),
                                array('slug' => 'fine_amount', 'label' => 'Fine Amount', 'type' => 'number', 'required' => 0),
                                array('slug' => 'condition_checkout', 'label' => 'Condition at Checkout', 'type' => 'select', 'required' => 1, 'options' => array('Excellent', 'Good', 'Fair', 'Poor')),
                                array('slug' => 'condition_return', 'label' => 'Condition at Return', 'type' => 'select', 'required' => 0, 'options' => array('Excellent', 'Good', 'Fair', 'Poor', 'Damaged', 'Lost')),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Checked Out', 'Returned', 'Overdue', 'Lost', 'Renewed'))
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Book',
                            'table_slug' => 'books',
                            'fields' => array('isbn', 'title', 'author', 'publisher', 'publication_year', 'category', 'genre', 'language', 'pages', 'location', 'total_copies', 'available_copies', 'acquisition_date', 'cost')
                        ),
                        array(
                            'name' => 'Member Registration',
                            'table_slug' => 'library_members',
                            'fields' => array('member_id', 'first_name', 'last_name', 'email', 'phone', 'address', 'date_of_birth', 'membership_type', 'registration_date', 'expiry_date', 'max_books')
                        ),
                        array(
                            'name' => 'Checkout Book',
                            'table_slug' => 'book_loans',
                            'fields' => array('loan_id', 'member_id', 'book_isbn', 'checkout_date', 'due_date', 'condition_checkout', 'notes')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Available Books',
                            'table_slug' => 'books',
                            'columns' => array('isbn', 'title', 'author', 'category', 'location', 'available_copies'),
                            'filters' => array(array('field' => 'available_copies', 'operator' => '>', 'value' => '0')),
                            'sort_order' => 'title ASC'
                        ),
                        array(
                            'name' => 'Overdue Books',
                            'table_slug' => 'book_loans',
                            'columns' => array('loan_id', 'member_id', 'book_isbn', 'due_date', 'fine_amount'),
                            'filters' => array(array('field' => 'due_date', 'operator' => '<', 'value' => 'CURDATE()'), array('field' => 'status', 'operator' => '=', 'value' => 'Checked Out')),
                            'sort_order' => 'due_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Construction Project Management
            array(
                'template_key' => 'construction_management',
                'name' => __('Construction Project Management', 'db-app-builder'),
                'description' => __('Construction project tracking with contractors, materials, schedules, and budget management.', 'db-app-builder'),
                'category' => 'specialized_industries',
                'subcategory' => 'construction',
                'icon' => 'dashicons-hammer',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'construction_projects',
                            'label' => 'Construction Projects',
                            'description' => 'Construction project information and tracking',
                            'fields' => array(
                                array('slug' => 'project_id', 'label' => 'Project ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'project_name', 'label' => 'Project Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'client_name', 'label' => 'Client Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'client_contact', 'label' => 'Client Contact', 'type' => 'text', 'required' => 1),
                                array('slug' => 'project_type', 'label' => 'Project Type', 'type' => 'select', 'required' => 1, 'options' => array('Residential', 'Commercial', 'Industrial', 'Infrastructure', 'Renovation', 'New Construction')),
                                array('slug' => 'project_address', 'label' => 'Project Address', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'start_date', 'label' => 'Start Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'planned_end_date', 'label' => 'Planned End Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'actual_end_date', 'label' => 'Actual End Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'total_budget', 'label' => 'Total Budget', 'type' => 'number', 'required' => 1),
                                array('slug' => 'spent_amount', 'label' => 'Amount Spent', 'type' => 'number', 'required' => 0),
                                array('slug' => 'project_manager', 'label' => 'Project Manager', 'type' => 'text', 'required' => 1),
                                array('slug' => 'permit_number', 'label' => 'Permit Number', 'type' => 'text', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Planning', 'Permits Pending', 'In Progress', 'On Hold', 'Completed', 'Cancelled'))
                            )
                        ),
                        array(
                            'slug' => 'contractors',
                            'label' => 'Contractors',
                            'description' => 'Contractor and subcontractor information',
                            'fields' => array(
                                array('slug' => 'contractor_id', 'label' => 'Contractor ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'company_name', 'label' => 'Company Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'contact_person', 'label' => 'Contact Person', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'address', 'label' => 'Address', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'specialty', 'label' => 'Specialty', 'type' => 'select', 'required' => 1, 'options' => array('General Contractor', 'Electrical', 'Plumbing', 'HVAC', 'Roofing', 'Flooring', 'Painting', 'Landscaping', 'Other')),
                                array('slug' => 'license_number', 'label' => 'License Number', 'type' => 'text', 'required' => 0),
                                array('slug' => 'insurance_expiry', 'label' => 'Insurance Expiry', 'type' => 'date', 'required' => 0),
                                array('slug' => 'hourly_rate', 'label' => 'Hourly Rate', 'type' => 'number', 'required' => 0),
                                array('slug' => 'rating', 'label' => 'Rating (1-5)', 'type' => 'number', 'required' => 0),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Blacklisted', 'Preferred'))
                            )
                        ),
                        array(
                            'slug' => 'project_assignments',
                            'label' => 'Project Assignments',
                            'description' => 'Contractor assignments to projects',
                            'fields' => array(
                                array('slug' => 'assignment_id', 'label' => 'Assignment ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'project_id', 'label' => 'Project', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'contractor_id', 'label' => 'Contractor', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'work_description', 'label' => 'Work Description', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'start_date', 'label' => 'Start Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'end_date', 'label' => 'End Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'contract_amount', 'label' => 'Contract Amount', 'type' => 'number', 'required' => 1),
                                array('slug' => 'paid_amount', 'label' => 'Paid Amount', 'type' => 'number', 'required' => 0),
                                array('slug' => 'completion_percentage', 'label' => 'Completion %', 'type' => 'number', 'required' => 0),
                                array('slug' => 'quality_rating', 'label' => 'Quality Rating (1-5)', 'type' => 'number', 'required' => 0),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Assigned', 'In Progress', 'Completed', 'On Hold', 'Cancelled'))
                            )
                        ),
                        array(
                            'slug' => 'materials',
                            'label' => 'Materials',
                            'description' => 'Construction materials and supplies tracking',
                            'fields' => array(
                                array('slug' => 'material_id', 'label' => 'Material ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'project_id', 'label' => 'Project', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'material_name', 'label' => 'Material Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'category', 'label' => 'Category', 'type' => 'select', 'required' => 1, 'options' => array('Concrete', 'Steel', 'Wood', 'Electrical', 'Plumbing', 'Insulation', 'Roofing', 'Flooring', 'Paint', 'Hardware', 'Other')),
                                array('slug' => 'supplier', 'label' => 'Supplier', 'type' => 'text', 'required' => 1),
                                array('slug' => 'unit', 'label' => 'Unit', 'type' => 'select', 'required' => 1, 'options' => array('Each', 'Square Feet', 'Linear Feet', 'Cubic Yards', 'Tons', 'Gallons', 'Pounds', 'Boxes', 'Pallets')),
                                array('slug' => 'quantity_ordered', 'label' => 'Quantity Ordered', 'type' => 'number', 'required' => 1),
                                array('slug' => 'quantity_received', 'label' => 'Quantity Received', 'type' => 'number', 'required' => 0),
                                array('slug' => 'unit_cost', 'label' => 'Unit Cost', 'type' => 'number', 'required' => 1),
                                array('slug' => 'total_cost', 'label' => 'Total Cost', 'type' => 'number', 'required' => 1),
                                array('slug' => 'order_date', 'label' => 'Order Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'delivery_date', 'label' => 'Delivery Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Ordered', 'Delivered', 'Partial Delivery', 'Cancelled', 'Returned'))
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Create Project',
                            'table_slug' => 'construction_projects',
                            'fields' => array('project_id', 'project_name', 'client_name', 'client_contact', 'project_type', 'project_address', 'description', 'start_date', 'planned_end_date', 'total_budget', 'project_manager', 'permit_number')
                        ),
                        array(
                            'name' => 'Add Contractor',
                            'table_slug' => 'contractors',
                            'fields' => array('contractor_id', 'company_name', 'contact_person', 'email', 'phone', 'address', 'specialty', 'license_number', 'insurance_expiry', 'hourly_rate', 'notes')
                        ),
                        array(
                            'name' => 'Assign Contractor',
                            'table_slug' => 'project_assignments',
                            'fields' => array('assignment_id', 'project_id', 'contractor_id', 'work_description', 'start_date', 'end_date', 'contract_amount', 'notes')
                        ),
                        array(
                            'name' => 'Order Materials',
                            'table_slug' => 'materials',
                            'fields' => array('material_id', 'project_id', 'material_name', 'category', 'supplier', 'unit', 'quantity_ordered', 'unit_cost', 'total_cost', 'order_date')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Active Projects',
                            'table_slug' => 'construction_projects',
                            'columns' => array('project_id', 'project_name', 'client_name', 'project_type', 'start_date', 'planned_end_date', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => 'NOT IN', 'value' => 'Completed,Cancelled')),
                            'sort_order' => 'start_date ASC'
                        ),
                        array(
                            'name' => 'Project Budget Overview',
                            'table_slug' => 'construction_projects',
                            'columns' => array('project_id', 'project_name', 'total_budget', 'spent_amount', 'status'),
                            'sort_order' => 'project_name ASC'
                        ),
                        array(
                            'name' => 'Pending Material Deliveries',
                            'table_slug' => 'materials',
                            'columns' => array('material_id', 'project_id', 'material_name', 'supplier', 'order_date', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => 'IN', 'value' => 'Ordered,Partial Delivery')),
                            'sort_order' => 'order_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Salon/Spa Management System
            array(
                'template_key' => 'salon_spa_management',
                'name' => __('Salon & Spa Management System', 'db-app-builder'),
                'description' => __('Beauty salon and spa operations with appointments, services, staff, and client management.', 'db-app-builder'),
                'category' => 'specialized_industries',
                'subcategory' => 'salon_spa',
                'icon' => 'dashicons-admin-appearance',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'salon_clients',
                            'label' => 'Salon Clients',
                            'description' => 'Client information and preferences',
                            'fields' => array(
                                array('slug' => 'client_id', 'label' => 'Client ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'date_of_birth', 'label' => 'Date of Birth', 'type' => 'date', 'required' => 0),
                                array('slug' => 'address', 'label' => 'Address', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'preferred_stylist', 'label' => 'Preferred Stylist', 'type' => 'text', 'required' => 0),
                                array('slug' => 'hair_type', 'label' => 'Hair Type', 'type' => 'select', 'required' => 0, 'options' => array('Straight', 'Wavy', 'Curly', 'Coily', 'Fine', 'Thick', 'Damaged', 'Color-Treated')),
                                array('slug' => 'skin_type', 'label' => 'Skin Type', 'type' => 'select', 'required' => 0, 'options' => array('Normal', 'Dry', 'Oily', 'Combination', 'Sensitive', 'Acne-Prone', 'Mature')),
                                array('slug' => 'allergies', 'label' => 'Allergies/Sensitivities', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'notes', 'label' => 'Client Notes', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'registration_date', 'label' => 'Registration Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'last_visit', 'label' => 'Last Visit', 'type' => 'date', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'VIP', 'Blacklisted'))
                            )
                        ),
                        array(
                            'slug' => 'salon_services',
                            'label' => 'Salon Services',
                            'description' => 'Available services and pricing',
                            'fields' => array(
                                array('slug' => 'service_id', 'label' => 'Service ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'service_name', 'label' => 'Service Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'category', 'label' => 'Category', 'type' => 'select', 'required' => 1, 'options' => array('Hair Cut', 'Hair Color', 'Hair Treatment', 'Facial', 'Massage', 'Manicure', 'Pedicure', 'Waxing', 'Eyebrow/Lash', 'Makeup', 'Other')),
                                array('slug' => 'duration_minutes', 'label' => 'Duration (minutes)', 'type' => 'number', 'required' => 1),
                                array('slug' => 'price', 'label' => 'Price', 'type' => 'number', 'required' => 1),
                                array('slug' => 'commission_rate', 'label' => 'Commission Rate (%)', 'type' => 'number', 'required' => 0),
                                array('slug' => 'requires_consultation', 'label' => 'Requires Consultation', 'type' => 'select', 'required' => 1, 'options' => array('Yes', 'No')),
                                array('slug' => 'available_staff', 'label' => 'Available Staff', 'type' => 'text', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Seasonal', 'Discontinued'))
                            )
                        ),
                        array(
                            'slug' => 'salon_appointments',
                            'label' => 'Salon Appointments',
                            'description' => 'Client appointments and bookings',
                            'fields' => array(
                                array('slug' => 'appointment_id', 'label' => 'Appointment ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'client_id', 'label' => 'Client', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'service_id', 'label' => 'Service', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'staff_member', 'label' => 'Staff Member', 'type' => 'text', 'required' => 1),
                                array('slug' => 'appointment_date', 'label' => 'Appointment Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'appointment_time', 'label' => 'Appointment Time', 'type' => 'time', 'required' => 1),
                                array('slug' => 'duration_minutes', 'label' => 'Duration (minutes)', 'type' => 'number', 'required' => 1),
                                array('slug' => 'service_price', 'label' => 'Service Price', 'type' => 'number', 'required' => 1),
                                array('slug' => 'tip_amount', 'label' => 'Tip Amount', 'type' => 'number', 'required' => 0),
                                array('slug' => 'total_amount', 'label' => 'Total Amount', 'type' => 'number', 'required' => 1),
                                array('slug' => 'payment_method', 'label' => 'Payment Method', 'type' => 'select', 'required' => 1, 'options' => array('Cash', 'Credit Card', 'Debit Card', 'Gift Card', 'Package Credit')),
                                array('slug' => 'notes', 'label' => 'Appointment Notes', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'reminder_sent', 'label' => 'Reminder Sent', 'type' => 'select', 'required' => 1, 'options' => array('Yes', 'No')),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Scheduled', 'Confirmed', 'In Progress', 'Completed', 'No Show', 'Cancelled'))
                            )
                        ),
                        array(
                            'slug' => 'salon_staff',
                            'label' => 'Salon Staff',
                            'description' => 'Staff member information and schedules',
                            'fields' => array(
                                array('slug' => 'staff_id', 'label' => 'Staff ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'position', 'label' => 'Position', 'type' => 'select', 'required' => 1, 'options' => array('Hair Stylist', 'Colorist', 'Esthetician', 'Massage Therapist', 'Nail Technician', 'Receptionist', 'Manager', 'Owner')),
                                array('slug' => 'specialties', 'label' => 'Specialties', 'type' => 'text', 'required' => 0),
                                array('slug' => 'hire_date', 'label' => 'Hire Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'hourly_rate', 'label' => 'Hourly Rate', 'type' => 'number', 'required' => 0),
                                array('slug' => 'commission_rate', 'label' => 'Commission Rate (%)', 'type' => 'number', 'required' => 0),
                                array('slug' => 'license_number', 'label' => 'License Number', 'type' => 'text', 'required' => 0),
                                array('slug' => 'license_expiry', 'label' => 'License Expiry', 'type' => 'date', 'required' => 0),
                                array('slug' => 'work_schedule', 'label' => 'Work Schedule', 'type' => 'text', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'On Leave', 'Terminated'))
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Client Registration',
                            'table_slug' => 'salon_clients',
                            'fields' => array('client_id', 'first_name', 'last_name', 'email', 'phone', 'date_of_birth', 'address', 'preferred_stylist', 'hair_type', 'skin_type', 'allergies', 'notes', 'registration_date')
                        ),
                        array(
                            'name' => 'Add Service',
                            'table_slug' => 'salon_services',
                            'fields' => array('service_id', 'service_name', 'description', 'category', 'duration_minutes', 'price', 'commission_rate', 'requires_consultation', 'available_staff')
                        ),
                        array(
                            'name' => 'Book Appointment',
                            'table_slug' => 'salon_appointments',
                            'fields' => array('appointment_id', 'client_id', 'service_id', 'staff_member', 'appointment_date', 'appointment_time', 'duration_minutes', 'service_price', 'notes')
                        ),
                        array(
                            'name' => 'Add Staff Member',
                            'table_slug' => 'salon_staff',
                            'fields' => array('staff_id', 'first_name', 'last_name', 'email', 'phone', 'position', 'specialties', 'hire_date', 'hourly_rate', 'commission_rate', 'license_number', 'license_expiry', 'work_schedule')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Today\'s Appointments',
                            'table_slug' => 'salon_appointments',
                            'columns' => array('appointment_id', 'client_id', 'service_id', 'staff_member', 'appointment_time', 'status'),
                            'filters' => array(array('field' => 'appointment_date', 'operator' => '=', 'value' => 'CURDATE()')),
                            'sort_order' => 'appointment_time ASC'
                        ),
                        array(
                            'name' => 'Active Services',
                            'table_slug' => 'salon_services',
                            'columns' => array('service_id', 'service_name', 'category', 'duration_minutes', 'price', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Active')),
                            'sort_order' => 'category ASC, service_name ASC'
                        ),
                        array(
                            'name' => 'Staff Schedule',
                            'table_slug' => 'salon_staff',
                            'columns' => array('staff_id', 'first_name', 'last_name', 'position', 'work_schedule', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Active')),
                            'sort_order' => 'last_name ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Vehicle Fleet Management
            array(
                'template_key' => 'fleet_management',
                'name' => __('Vehicle Fleet Management System', 'db-app-builder'),
                'description' => __('Complete fleet management with vehicles, drivers, maintenance, and fuel tracking.', 'db-app-builder'),
                'category' => 'specialized_industries',
                'subcategory' => 'transportation',
                'icon' => 'dashicons-car',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'vehicles',
                            'label' => 'Vehicles',
                            'description' => 'Fleet vehicle information and details',
                            'fields' => array(
                                array('slug' => 'vehicle_id', 'label' => 'Vehicle ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'license_plate', 'label' => 'License Plate', 'type' => 'text', 'required' => 1),
                                array('slug' => 'vin', 'label' => 'VIN', 'type' => 'text', 'required' => 1),
                                array('slug' => 'make', 'label' => 'Make', 'type' => 'text', 'required' => 1),
                                array('slug' => 'model', 'label' => 'Model', 'type' => 'text', 'required' => 1),
                                array('slug' => 'year', 'label' => 'Year', 'type' => 'number', 'required' => 1),
                                array('slug' => 'color', 'label' => 'Color', 'type' => 'text', 'required' => 0),
                                array('slug' => 'vehicle_type', 'label' => 'Vehicle Type', 'type' => 'select', 'required' => 1, 'options' => array('Car', 'Truck', 'Van', 'SUV', 'Motorcycle', 'Bus', 'Trailer', 'Heavy Equipment')),
                                array('slug' => 'fuel_type', 'label' => 'Fuel Type', 'type' => 'select', 'required' => 1, 'options' => array('Gasoline', 'Diesel', 'Electric', 'Hybrid', 'CNG', 'LPG')),
                                array('slug' => 'purchase_date', 'label' => 'Purchase Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'purchase_price', 'label' => 'Purchase Price', 'type' => 'number', 'required' => 0),
                                array('slug' => 'current_mileage', 'label' => 'Current Mileage', 'type' => 'number', 'required' => 1),
                                array('slug' => 'insurance_company', 'label' => 'Insurance Company', 'type' => 'text', 'required' => 0),
                                array('slug' => 'insurance_policy', 'label' => 'Insurance Policy #', 'type' => 'text', 'required' => 0),
                                array('slug' => 'insurance_expiry', 'label' => 'Insurance Expiry', 'type' => 'date', 'required' => 0),
                                array('slug' => 'registration_expiry', 'label' => 'Registration Expiry', 'type' => 'date', 'required' => 0),
                                array('slug' => 'assigned_driver', 'label' => 'Assigned Driver', 'type' => 'text', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'In Maintenance', 'Out of Service', 'Sold', 'Retired'))
                            )
                        ),
                        array(
                            'slug' => 'drivers',
                            'label' => 'Drivers',
                            'description' => 'Driver information and records',
                            'fields' => array(
                                array('slug' => 'driver_id', 'label' => 'Driver ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'address', 'label' => 'Address', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'date_of_birth', 'label' => 'Date of Birth', 'type' => 'date', 'required' => 1),
                                array('slug' => 'hire_date', 'label' => 'Hire Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'license_number', 'label' => 'License Number', 'type' => 'text', 'required' => 1),
                                array('slug' => 'license_class', 'label' => 'License Class', 'type' => 'select', 'required' => 1, 'options' => array('Class A', 'Class B', 'Class C', 'CDL A', 'CDL B', 'CDL C', 'Motorcycle', 'Other')),
                                array('slug' => 'license_expiry', 'label' => 'License Expiry', 'type' => 'date', 'required' => 1),
                                array('slug' => 'medical_cert_expiry', 'label' => 'Medical Cert Expiry', 'type' => 'date', 'required' => 0),
                                array('slug' => 'emergency_contact', 'label' => 'Emergency Contact', 'type' => 'text', 'required' => 1),
                                array('slug' => 'emergency_phone', 'label' => 'Emergency Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'On Leave', 'Suspended', 'Terminated'))
                            )
                        ),
                        array(
                            'slug' => 'vehicle_maintenance',
                            'label' => 'Vehicle Maintenance',
                            'description' => 'Vehicle maintenance and service records',
                            'fields' => array(
                                array('slug' => 'maintenance_id', 'label' => 'Maintenance ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'vehicle_id', 'label' => 'Vehicle', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'maintenance_type', 'label' => 'Maintenance Type', 'type' => 'select', 'required' => 1, 'options' => array('Oil Change', 'Tire Rotation', 'Brake Service', 'Transmission Service', 'Engine Repair', 'Inspection', 'Preventive Maintenance', 'Emergency Repair', 'Other')),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'service_date', 'label' => 'Service Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'mileage_at_service', 'label' => 'Mileage at Service', 'type' => 'number', 'required' => 1),
                                array('slug' => 'service_provider', 'label' => 'Service Provider', 'type' => 'text', 'required' => 1),
                                array('slug' => 'cost', 'label' => 'Cost', 'type' => 'number', 'required' => 1),
                                array('slug' => 'parts_replaced', 'label' => 'Parts Replaced', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'next_service_date', 'label' => 'Next Service Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'next_service_mileage', 'label' => 'Next Service Mileage', 'type' => 'number', 'required' => 0),
                                array('slug' => 'warranty_info', 'label' => 'Warranty Info', 'type' => 'text', 'required' => 0),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0)
                            )
                        ),
                        array(
                            'slug' => 'fuel_records',
                            'label' => 'Fuel Records',
                            'description' => 'Vehicle fuel consumption tracking',
                            'fields' => array(
                                array('slug' => 'fuel_id', 'label' => 'Fuel Record ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'vehicle_id', 'label' => 'Vehicle', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'driver_id', 'label' => 'Driver', 'type' => 'relationship', 'required' => 0),
                                array('slug' => 'fuel_date', 'label' => 'Fuel Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'odometer_reading', 'label' => 'Odometer Reading', 'type' => 'number', 'required' => 1),
                                array('slug' => 'gallons_liters', 'label' => 'Gallons/Liters', 'type' => 'number', 'required' => 1),
                                array('slug' => 'cost_per_unit', 'label' => 'Cost per Unit', 'type' => 'number', 'required' => 1),
                                array('slug' => 'total_cost', 'label' => 'Total Cost', 'type' => 'number', 'required' => 1),
                                array('slug' => 'fuel_station', 'label' => 'Fuel Station', 'type' => 'text', 'required' => 0),
                                array('slug' => 'payment_method', 'label' => 'Payment Method', 'type' => 'select', 'required' => 1, 'options' => array('Fleet Card', 'Cash', 'Credit Card', 'Company Account')),
                                array('slug' => 'receipt_number', 'label' => 'Receipt Number', 'type' => 'text', 'required' => 0),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Vehicle',
                            'table_slug' => 'vehicles',
                            'fields' => array('vehicle_id', 'license_plate', 'vin', 'make', 'model', 'year', 'color', 'vehicle_type', 'fuel_type', 'purchase_date', 'purchase_price', 'current_mileage', 'insurance_company', 'insurance_policy', 'insurance_expiry', 'registration_expiry', 'assigned_driver')
                        ),
                        array(
                            'name' => 'Add Driver',
                            'table_slug' => 'drivers',
                            'fields' => array('driver_id', 'first_name', 'last_name', 'email', 'phone', 'address', 'date_of_birth', 'hire_date', 'license_number', 'license_class', 'license_expiry', 'medical_cert_expiry', 'emergency_contact', 'emergency_phone')
                        ),
                        array(
                            'name' => 'Record Maintenance',
                            'table_slug' => 'vehicle_maintenance',
                            'fields' => array('maintenance_id', 'vehicle_id', 'maintenance_type', 'description', 'service_date', 'mileage_at_service', 'service_provider', 'cost', 'parts_replaced', 'next_service_date', 'next_service_mileage', 'warranty_info', 'notes')
                        ),
                        array(
                            'name' => 'Record Fuel',
                            'table_slug' => 'fuel_records',
                            'fields' => array('fuel_id', 'vehicle_id', 'driver_id', 'fuel_date', 'odometer_reading', 'gallons_liters', 'cost_per_unit', 'total_cost', 'fuel_station', 'payment_method', 'receipt_number', 'notes')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Active Fleet',
                            'table_slug' => 'vehicles',
                            'columns' => array('vehicle_id', 'license_plate', 'make', 'model', 'year', 'assigned_driver', 'current_mileage', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Active')),
                            'sort_order' => 'vehicle_id ASC'
                        ),
                        array(
                            'name' => 'Maintenance Due',
                            'table_slug' => 'vehicle_maintenance',
                            'columns' => array('maintenance_id', 'vehicle_id', 'maintenance_type', 'next_service_date', 'next_service_mileage'),
                            'filters' => array(array('field' => 'next_service_date', 'operator' => '<=', 'value' => 'DATE_ADD(CURDATE(), INTERVAL 30 DAY)')),
                            'sort_order' => 'next_service_date ASC'
                        ),
                        array(
                            'name' => 'Fuel Consumption',
                            'table_slug' => 'fuel_records',
                            'columns' => array('fuel_id', 'vehicle_id', 'fuel_date', 'gallons_liters', 'total_cost', 'odometer_reading'),
                            'sort_order' => 'fuel_date DESC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Customer Support Ticketing
            array(
                'template_key' => 'customer_support',
                'name' => __('Customer Support Ticketing System', 'db-app-builder'),
                'description' => __('Help desk system with ticket management, customer support, and resolution tracking.', 'db-app-builder'),
                'category' => 'customer_sales',
                'subcategory' => 'support',
                'icon' => 'dashicons-sos',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'support_tickets',
                            'label' => 'Support Tickets',
                            'description' => 'Customer support ticket tracking',
                            'fields' => array(
                                array('slug' => 'ticket_id', 'label' => 'Ticket ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'customer_name', 'label' => 'Customer Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'customer_email', 'label' => 'Customer Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'subject', 'label' => 'Subject', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'priority', 'label' => 'Priority', 'type' => 'select', 'required' => 1, 'options' => array('Low', 'Medium', 'High', 'Critical')),
                                array('slug' => 'category', 'label' => 'Category', 'type' => 'select', 'required' => 1, 'options' => array('Technical', 'Billing', 'General', 'Feature Request', 'Bug Report')),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Open', 'In Progress', 'Waiting for Customer', 'Resolved', 'Closed')),
                                array('slug' => 'assigned_to', 'label' => 'Assigned To', 'type' => 'text', 'required' => 0),
                                array('slug' => 'created_date', 'label' => 'Created Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'resolved_date', 'label' => 'Resolved Date', 'type' => 'date', 'required' => 0)
                            )
                        ),
                        array(
                            'slug' => 'ticket_responses',
                            'label' => 'Ticket Responses',
                            'description' => 'Responses and communications for tickets',
                            'fields' => array(
                                array('slug' => 'ticket_id', 'label' => 'Ticket', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'response_from', 'label' => 'Response From', 'type' => 'select', 'required' => 1, 'options' => array('Customer', 'Support Agent', 'System')),
                                array('slug' => 'responder_name', 'label' => 'Responder Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'response_text', 'label' => 'Response', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'response_date', 'label' => 'Response Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'is_internal', 'label' => 'Internal Note', 'type' => 'select', 'required' => 1, 'options' => array('No', 'Yes'))
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Create Support Ticket',
                            'table_slug' => 'support_tickets',
                            'fields' => array('ticket_id', 'customer_name', 'customer_email', 'subject', 'description', 'priority', 'category')
                        ),
                        array(
                            'name' => 'Add Response',
                            'table_slug' => 'ticket_responses',
                            'fields' => array('ticket_id', 'response_from', 'responder_name', 'response_text', 'is_internal')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Open Tickets',
                            'table_slug' => 'support_tickets',
                            'columns' => array('ticket_id', 'customer_name', 'subject', 'priority', 'status', 'assigned_to'),
                            'filters' => array(array('field' => 'status', 'operator' => 'NOT IN', 'value' => 'Resolved,Closed')),
                            'sort_order' => 'priority DESC, created_date ASC'
                        ),
                        array(
                            'name' => 'High Priority Tickets',
                            'table_slug' => 'support_tickets',
                            'columns' => array('ticket_id', 'customer_name', 'subject', 'status', 'created_date'),
                            'filters' => array(array('field' => 'priority', 'operator' => 'IN', 'value' => 'High,Critical')),
                            'sort_order' => 'created_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Task Management
            array(
                'template_key' => 'task_management',
                'name' => __('Task Management System', 'db-app-builder'),
                'description' => __('Team task organization with assignments, deadlines, and progress tracking.', 'db-app-builder'),
                'category' => 'project_task',
                'subcategory' => 'task_management',
                'icon' => 'dashicons-list-view',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'task_lists',
                            'label' => 'Task Lists',
                            'description' => 'Task list organization',
                            'fields' => array(
                                array('slug' => 'list_name', 'label' => 'List Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'color', 'label' => 'Color', 'type' => 'select', 'required' => 1, 'options' => array('Blue', 'Green', 'Red', 'Yellow', 'Purple', 'Orange')),
                                array('slug' => 'is_active', 'label' => 'Active', 'type' => 'select', 'required' => 1, 'options' => array('Yes', 'No'))
                            )
                        ),
                        array(
                            'slug' => 'tasks',
                            'label' => 'Tasks',
                            'description' => 'Individual tasks and assignments',
                            'fields' => array(
                                array('slug' => 'task_title', 'label' => 'Task Title', 'type' => 'text', 'required' => 1),
                                array('slug' => 'list_id', 'label' => 'Task List', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'assigned_to', 'label' => 'Assigned To', 'type' => 'text', 'required' => 0),
                                array('slug' => 'due_date', 'label' => 'Due Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'priority', 'label' => 'Priority', 'type' => 'select', 'required' => 1, 'options' => array('Low', 'Medium', 'High', 'Critical')),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('To Do', 'In Progress', 'Review', 'Done')),
                                array('slug' => 'estimated_hours', 'label' => 'Estimated Hours', 'type' => 'number', 'required' => 0),
                                array('slug' => 'actual_hours', 'label' => 'Actual Hours', 'type' => 'number', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Create Task List',
                            'table_slug' => 'task_lists',
                            'fields' => array('list_name', 'description', 'color', 'is_active')
                        ),
                        array(
                            'name' => 'Add Task',
                            'table_slug' => 'tasks',
                            'fields' => array('task_title', 'list_id', 'description', 'assigned_to', 'due_date', 'priority', 'estimated_hours')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'My Tasks',
                            'table_slug' => 'tasks',
                            'columns' => array('task_title', 'due_date', 'priority', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => '!=', 'value' => 'Done')),
                            'sort_order' => 'due_date ASC'
                        ),
                        array(
                            'name' => 'Overdue Tasks',
                            'table_slug' => 'tasks',
                            'columns' => array('task_title', 'assigned_to', 'due_date', 'priority'),
                            'filters' => array(
                                array('field' => 'due_date', 'operator' => '<', 'value' => 'TODAY()'),
                                array('field' => 'status', 'operator' => '!=', 'value' => 'Done')
                            ),
                            'sort_order' => 'due_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Performance Management
            array(
                'template_key' => 'performance_management',
                'name' => __('Performance Management System', 'db-app-builder'),
                'description' => __('Employee performance tracking with evaluations, goals, and development plans.', 'db-app-builder'),
                'category' => 'human_resources',
                'subcategory' => 'performance',
                'icon' => 'dashicons-chart-line',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'performance_reviews',
                            'label' => 'Performance Reviews',
                            'description' => 'Employee performance evaluations',
                            'fields' => array(
                                array('slug' => 'employee_id', 'label' => 'Employee ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'employee_name', 'label' => 'Employee Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'reviewer_name', 'label' => 'Reviewer Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'review_period', 'label' => 'Review Period', 'type' => 'select', 'required' => 1, 'options' => array('Q1', 'Q2', 'Q3', 'Q4', 'Mid-Year', 'Annual')),
                                array('slug' => 'review_year', 'label' => 'Review Year', 'type' => 'number', 'required' => 1),
                                array('slug' => 'overall_rating', 'label' => 'Overall Rating', 'type' => 'select', 'required' => 1, 'options' => array('Exceeds Expectations', 'Meets Expectations', 'Below Expectations', 'Unsatisfactory')),
                                array('slug' => 'strengths', 'label' => 'Strengths', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'areas_for_improvement', 'label' => 'Areas for Improvement', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'goals_achieved', 'label' => 'Goals Achieved', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'development_plan', 'label' => 'Development Plan', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'review_date', 'label' => 'Review Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Draft', 'In Progress', 'Completed', 'Approved'))
                            )
                        ),
                        array(
                            'slug' => 'employee_goals',
                            'label' => 'Employee Goals',
                            'description' => 'Individual employee goals and objectives',
                            'fields' => array(
                                array('slug' => 'employee_id', 'label' => 'Employee ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'goal_title', 'label' => 'Goal Title', 'type' => 'text', 'required' => 1),
                                array('slug' => 'goal_description', 'label' => 'Goal Description', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'goal_category', 'label' => 'Goal Category', 'type' => 'select', 'required' => 1, 'options' => array('Performance', 'Development', 'Behavioral', 'Project-based', 'Learning')),
                                array('slug' => 'target_date', 'label' => 'Target Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'progress_percentage', 'label' => 'Progress (%)', 'type' => 'number', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Not Started', 'In Progress', 'Completed', 'Deferred', 'Cancelled')),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Performance Review',
                            'table_slug' => 'performance_reviews',
                            'fields' => array('employee_id', 'employee_name', 'reviewer_name', 'review_period', 'review_year', 'overall_rating', 'strengths', 'areas_for_improvement', 'goals_achieved', 'development_plan')
                        ),
                        array(
                            'name' => 'Set Employee Goal',
                            'table_slug' => 'employee_goals',
                            'fields' => array('employee_id', 'goal_title', 'goal_description', 'goal_category', 'target_date', 'progress_percentage')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Recent Reviews',
                            'table_slug' => 'performance_reviews',
                            'columns' => array('employee_name', 'review_period', 'review_year', 'overall_rating', 'status'),
                            'sort_order' => 'review_date DESC'
                        ),
                        array(
                            'name' => 'Active Goals',
                            'table_slug' => 'employee_goals',
                            'columns' => array('employee_id', 'goal_title', 'goal_category', 'target_date', 'progress_percentage', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => 'IN', 'value' => 'Not Started,In Progress')),
                            'sort_order' => 'target_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Asset Management
            array(
                'template_key' => 'asset_management',
                'name' => __('Asset Management System', 'db-app-builder'),
                'description' => __('Company asset tracking with maintenance scheduling and depreciation management.', 'db-app-builder'),
                'category' => 'inventory_operations',
                'subcategory' => 'asset_management',
                'icon' => 'dashicons-admin-tools',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'company_assets',
                            'label' => 'Company Assets',
                            'description' => 'Company asset inventory and tracking',
                            'fields' => array(
                                array('slug' => 'asset_id', 'label' => 'Asset ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'asset_name', 'label' => 'Asset Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'category', 'label' => 'Category', 'type' => 'select', 'required' => 1, 'options' => array('IT Equipment', 'Office Furniture', 'Vehicles', 'Machinery', 'Software', 'Other')),
                                array('slug' => 'purchase_date', 'label' => 'Purchase Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'purchase_cost', 'label' => 'Purchase Cost', 'type' => 'number', 'required' => 1),
                                array('slug' => 'current_value', 'label' => 'Current Value', 'type' => 'number', 'required' => 0),
                                array('slug' => 'location', 'label' => 'Location', 'type' => 'text', 'required' => 1),
                                array('slug' => 'assigned_to', 'label' => 'Assigned To', 'type' => 'text', 'required' => 0),
                                array('slug' => 'warranty_expiry', 'label' => 'Warranty Expiry', 'type' => 'date', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Under Maintenance', 'Disposed', 'Lost/Stolen'))
                            )
                        ),
                        array(
                            'slug' => 'maintenance_records',
                            'label' => 'Maintenance Records',
                            'description' => 'Asset maintenance and service records',
                            'fields' => array(
                                array('slug' => 'asset_id', 'label' => 'Asset', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'maintenance_type', 'label' => 'Maintenance Type', 'type' => 'select', 'required' => 1, 'options' => array('Preventive', 'Corrective', 'Emergency', 'Upgrade', 'Inspection')),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'service_provider', 'label' => 'Service Provider', 'type' => 'text', 'required' => 0),
                                array('slug' => 'maintenance_date', 'label' => 'Maintenance Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'cost', 'label' => 'Cost', 'type' => 'number', 'required' => 0),
                                array('slug' => 'next_maintenance_date', 'label' => 'Next Maintenance Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Asset',
                            'table_slug' => 'company_assets',
                            'fields' => array('asset_id', 'asset_name', 'description', 'category', 'purchase_date', 'purchase_cost', 'location', 'assigned_to', 'warranty_expiry')
                        ),
                        array(
                            'name' => 'Maintenance Record',
                            'table_slug' => 'maintenance_records',
                            'fields' => array('asset_id', 'maintenance_type', 'description', 'service_provider', 'maintenance_date', 'cost', 'next_maintenance_date', 'notes')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'All Assets',
                            'table_slug' => 'company_assets',
                            'columns' => array('asset_id', 'asset_name', 'category', 'location', 'assigned_to', 'status'),
                            'sort_order' => 'asset_name ASC'
                        ),
                        array(
                            'name' => 'Maintenance Due',
                            'table_slug' => 'maintenance_records',
                            'columns' => array('asset_id', 'maintenance_type', 'next_maintenance_date'),
                            'filters' => array(array('field' => 'next_maintenance_date', 'operator' => '<=', 'value' => 'DATE_ADD(CURDATE(), INTERVAL 30 DAY)')),
                            'sort_order' => 'next_maintenance_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Student Management
            array(
                'template_key' => 'student_management',
                'name' => __('Student Management System', 'db-app-builder'),
                'description' => __('Educational institution management with students, courses, grades, and enrollment tracking.', 'db-app-builder'),
                'category' => 'education_training',
                'subcategory' => 'student_management',
                'icon' => 'dashicons-welcome-learn-more',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'students',
                            'label' => 'Students',
                            'description' => 'Student information and records',
                            'fields' => array(
                                array('slug' => 'student_id', 'label' => 'Student ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 0),
                                array('slug' => 'date_of_birth', 'label' => 'Date of Birth', 'type' => 'date', 'required' => 1),
                                array('slug' => 'address', 'label' => 'Address', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'emergency_contact', 'label' => 'Emergency Contact', 'type' => 'text', 'required' => 1),
                                array('slug' => 'emergency_phone', 'label' => 'Emergency Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'enrollment_date', 'label' => 'Enrollment Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'program', 'label' => 'Program', 'type' => 'select', 'required' => 1, 'options' => array('Computer Science', 'Business Administration', 'Engineering', 'Arts', 'Sciences', 'Other')),
                                array('slug' => 'year_level', 'label' => 'Year Level', 'type' => 'select', 'required' => 1, 'options' => array('1st Year', '2nd Year', '3rd Year', '4th Year', 'Graduate')),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Graduated', 'Dropped', 'Suspended'))
                            )
                        ),
                        array(
                            'slug' => 'courses',
                            'label' => 'Courses',
                            'description' => 'Course catalog and information',
                            'fields' => array(
                                array('slug' => 'course_code', 'label' => 'Course Code', 'type' => 'text', 'required' => 1),
                                array('slug' => 'course_name', 'label' => 'Course Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'description', 'label' => 'Description', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'credits', 'label' => 'Credits', 'type' => 'number', 'required' => 1),
                                array('slug' => 'instructor', 'label' => 'Instructor', 'type' => 'text', 'required' => 1),
                                array('slug' => 'semester', 'label' => 'Semester', 'type' => 'select', 'required' => 1, 'options' => array('Fall', 'Spring', 'Summer')),
                                array('slug' => 'year', 'label' => 'Year', 'type' => 'number', 'required' => 1),
                                array('slug' => 'max_enrollment', 'label' => 'Max Enrollment', 'type' => 'number', 'required' => 1),
                                array('slug' => 'schedule', 'label' => 'Schedule', 'type' => 'text', 'required' => 1),
                                array('slug' => 'room', 'label' => 'Room', 'type' => 'text', 'required' => 1)
                            )
                        ),
                        array(
                            'slug' => 'enrollments',
                            'label' => 'Course Enrollments',
                            'description' => 'Student course enrollments and grades',
                            'fields' => array(
                                array('slug' => 'student_id', 'label' => 'Student', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'course_id', 'label' => 'Course', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'enrollment_date', 'label' => 'Enrollment Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'midterm_grade', 'label' => 'Midterm Grade', 'type' => 'text', 'required' => 0),
                                array('slug' => 'final_grade', 'label' => 'Final Grade', 'type' => 'text', 'required' => 0),
                                array('slug' => 'attendance_percentage', 'label' => 'Attendance %', 'type' => 'number', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Enrolled', 'Completed', 'Dropped', 'Failed'))
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Student Registration',
                            'table_slug' => 'students',
                            'fields' => array('student_id', 'first_name', 'last_name', 'email', 'phone', 'date_of_birth', 'address', 'emergency_contact', 'emergency_phone', 'program', 'year_level')
                        ),
                        array(
                            'name' => 'Add Course',
                            'table_slug' => 'courses',
                            'fields' => array('course_code', 'course_name', 'description', 'credits', 'instructor', 'semester', 'year', 'max_enrollment', 'schedule', 'room')
                        ),
                        array(
                            'name' => 'Course Enrollment',
                            'table_slug' => 'enrollments',
                            'fields' => array('student_id', 'course_id', 'enrollment_date')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Active Students',
                            'table_slug' => 'students',
                            'columns' => array('student_id', 'first_name', 'last_name', 'program', 'year_level', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Active')),
                            'sort_order' => 'last_name ASC'
                        ),
                        array(
                            'name' => 'Current Enrollments',
                            'table_slug' => 'enrollments',
                            'columns' => array('student_id', 'course_id', 'midterm_grade', 'final_grade', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => 'IN', 'value' => 'Enrolled,Completed')),
                            'sort_order' => 'enrollment_date DESC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Library Management
            array(
                'template_key' => 'library_management',
                'name' => __('Library Management System', 'db-app-builder'),
                'description' => __('Complete library operations with book tracking, member management, and loan processing.', 'db-app-builder'),
                'category' => 'education_training',
                'subcategory' => 'library_management',
                'icon' => 'dashicons-book',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'books',
                            'label' => 'Books',
                            'description' => 'Library book catalog',
                            'fields' => array(
                                array('slug' => 'isbn', 'label' => 'ISBN', 'type' => 'text', 'required' => 1),
                                array('slug' => 'title', 'label' => 'Title', 'type' => 'text', 'required' => 1),
                                array('slug' => 'author', 'label' => 'Author', 'type' => 'text', 'required' => 1),
                                array('slug' => 'publisher', 'label' => 'Publisher', 'type' => 'text', 'required' => 0),
                                array('slug' => 'publication_year', 'label' => 'Publication Year', 'type' => 'number', 'required' => 0),
                                array('slug' => 'category', 'label' => 'Category', 'type' => 'select', 'required' => 1, 'options' => array('Fiction', 'Non-Fiction', 'Science', 'History', 'Biography', 'Reference', 'Children', 'Other')),
                                array('slug' => 'genre', 'label' => 'Genre', 'type' => 'text', 'required' => 0),
                                array('slug' => 'language', 'label' => 'Language', 'type' => 'select', 'required' => 1, 'options' => array('English', 'Spanish', 'French', 'German', 'Other')),
                                array('slug' => 'pages', 'label' => 'Pages', 'type' => 'number', 'required' => 0),
                                array('slug' => 'location', 'label' => 'Shelf Location', 'type' => 'text', 'required' => 1),
                                array('slug' => 'total_copies', 'label' => 'Total Copies', 'type' => 'number', 'required' => 1),
                                array('slug' => 'available_copies', 'label' => 'Available Copies', 'type' => 'number', 'required' => 1),
                                array('slug' => 'acquisition_date', 'label' => 'Acquisition Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'cost', 'label' => 'Cost', 'type' => 'number', 'required' => 0)
                            )
                        ),
                        array(
                            'slug' => 'library_members',
                            'label' => 'Library Members',
                            'description' => 'Library membership records',
                            'fields' => array(
                                array('slug' => 'member_id', 'label' => 'Member ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 0),
                                array('slug' => 'address', 'label' => 'Address', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'date_of_birth', 'label' => 'Date of Birth', 'type' => 'date', 'required' => 0),
                                array('slug' => 'membership_type', 'label' => 'Membership Type', 'type' => 'select', 'required' => 1, 'options' => array('Student', 'Faculty', 'Staff', 'Public', 'Senior')),
                                array('slug' => 'registration_date', 'label' => 'Registration Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'expiry_date', 'label' => 'Expiry Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'max_books', 'label' => 'Max Books Allowed', 'type' => 'number', 'required' => 1)
                            )
                        ),
                        array(
                            'slug' => 'book_loans',
                            'label' => 'Book Loans',
                            'description' => 'Book checkout and return tracking',
                            'fields' => array(
                                array('slug' => 'loan_id', 'label' => 'Loan ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'member_id', 'label' => 'Member', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'book_isbn', 'label' => 'Book', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'checkout_date', 'label' => 'Checkout Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'due_date', 'label' => 'Due Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'return_date', 'label' => 'Return Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'condition_checkout', 'label' => 'Condition at Checkout', 'type' => 'select', 'required' => 1, 'options' => array('Excellent', 'Good', 'Fair', 'Poor')),
                                array('slug' => 'condition_return', 'label' => 'Condition at Return', 'type' => 'select', 'required' => 0, 'options' => array('Excellent', 'Good', 'Fair', 'Poor', 'Damaged')),
                                array('slug' => 'fine_amount', 'label' => 'Fine Amount', 'type' => 'number', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Checked Out', 'Returned', 'Overdue', 'Lost')),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Book',
                            'table_slug' => 'books',
                            'fields' => array('isbn', 'title', 'author', 'publisher', 'publication_year', 'category', 'genre', 'language', 'pages', 'location', 'total_copies', 'available_copies', 'acquisition_date', 'cost')
                        ),
                        array(
                            'name' => 'Member Registration',
                            'table_slug' => 'library_members',
                            'fields' => array('member_id', 'first_name', 'last_name', 'email', 'phone', 'address', 'date_of_birth', 'membership_type', 'registration_date', 'expiry_date', 'max_books')
                        ),
                        array(
                            'name' => 'Checkout Book',
                            'table_slug' => 'book_loans',
                            'fields' => array('loan_id', 'member_id', 'book_isbn', 'checkout_date', 'due_date', 'condition_checkout', 'notes')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Available Books',
                            'table_slug' => 'books',
                            'columns' => array('isbn', 'title', 'author', 'category', 'available_copies', 'location'),
                            'filters' => array(array('field' => 'available_copies', 'operator' => '>', 'value' => '0')),
                            'sort_order' => 'title ASC'
                        ),
                        array(
                            'name' => 'Overdue Books',
                            'table_slug' => 'book_loans',
                            'columns' => array('loan_id', 'member_id', 'book_isbn', 'due_date', 'checkout_date'),
                            'filters' => array(
                                array('field' => 'due_date', 'operator' => '<', 'value' => 'CURDATE()'),
                                array('field' => 'status', 'operator' => '=', 'value' => 'Checked Out')
                            ),
                            'sort_order' => 'due_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Patient Management
            array(
                'template_key' => 'patient_management',
                'name' => __('Patient Management System', 'db-app-builder'),
                'description' => __('Medical practice management with patient records, appointments, and treatment tracking.', 'db-app-builder'),
                'category' => 'healthcare_medical',
                'subcategory' => 'patient_management',
                'icon' => 'dashicons-heart',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'patients',
                            'label' => 'Patients',
                            'description' => 'Patient information and medical records',
                            'fields' => array(
                                array('slug' => 'patient_id', 'label' => 'Patient ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'date_of_birth', 'label' => 'Date of Birth', 'type' => 'date', 'required' => 1),
                                array('slug' => 'gender', 'label' => 'Gender', 'type' => 'select', 'required' => 1, 'options' => array('Male', 'Female', 'Other', 'Prefer not to say')),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 0),
                                array('slug' => 'address', 'label' => 'Address', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'emergency_contact', 'label' => 'Emergency Contact', 'type' => 'text', 'required' => 1),
                                array('slug' => 'emergency_phone', 'label' => 'Emergency Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'insurance_provider', 'label' => 'Insurance Provider', 'type' => 'text', 'required' => 0),
                                array('slug' => 'insurance_number', 'label' => 'Insurance Number', 'type' => 'text', 'required' => 0),
                                array('slug' => 'blood_type', 'label' => 'Blood Type', 'type' => 'select', 'required' => 0, 'options' => array('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-', 'Unknown')),
                                array('slug' => 'allergies', 'label' => 'Allergies', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'medical_history', 'label' => 'Medical History', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Inactive', 'Deceased'))
                            )
                        ),
                        array(
                            'slug' => 'appointments',
                            'label' => 'Appointments',
                            'description' => 'Patient appointment scheduling',
                            'fields' => array(
                                array('slug' => 'appointment_id', 'label' => 'Appointment ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'patient_id', 'label' => 'Patient', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'appointment_date', 'label' => 'Appointment Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'appointment_time', 'label' => 'Appointment Time', 'type' => 'time', 'required' => 1),
                                array('slug' => 'duration_minutes', 'label' => 'Duration (Minutes)', 'type' => 'number', 'required' => 1),
                                array('slug' => 'appointment_type', 'label' => 'Appointment Type', 'type' => 'select', 'required' => 1, 'options' => array('Consultation', 'Follow-up', 'Check-up', 'Emergency', 'Surgery', 'Therapy')),
                                array('slug' => 'doctor_name', 'label' => 'Doctor Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'reason', 'label' => 'Reason for Visit', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Scheduled', 'Confirmed', 'In Progress', 'Completed', 'Cancelled', 'No Show')),
                                array('slug' => 'notes', 'label' => 'Notes', 'type' => 'textarea', 'required' => 0)
                            )
                        ),
                        array(
                            'slug' => 'treatments',
                            'label' => 'Treatments',
                            'description' => 'Patient treatment records',
                            'fields' => array(
                                array('slug' => 'treatment_id', 'label' => 'Treatment ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'patient_id', 'label' => 'Patient', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'appointment_id', 'label' => 'Appointment', 'type' => 'relationship', 'required' => 0),
                                array('slug' => 'treatment_date', 'label' => 'Treatment Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'diagnosis', 'label' => 'Diagnosis', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'treatment_description', 'label' => 'Treatment Description', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'medications', 'label' => 'Medications Prescribed', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'follow_up_required', 'label' => 'Follow-up Required', 'type' => 'select', 'required' => 1, 'options' => array('Yes', 'No')),
                                array('slug' => 'follow_up_date', 'label' => 'Follow-up Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'doctor_name', 'label' => 'Doctor Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'cost', 'label' => 'Treatment Cost', 'type' => 'number', 'required' => 0)
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Patient Registration',
                            'table_slug' => 'patients',
                            'fields' => array('patient_id', 'first_name', 'last_name', 'date_of_birth', 'gender', 'phone', 'email', 'address', 'emergency_contact', 'emergency_phone', 'insurance_provider', 'insurance_number', 'blood_type', 'allergies', 'medical_history')
                        ),
                        array(
                            'name' => 'Schedule Appointment',
                            'table_slug' => 'appointments',
                            'fields' => array('appointment_id', 'patient_id', 'appointment_date', 'appointment_time', 'duration_minutes', 'appointment_type', 'doctor_name', 'reason')
                        ),
                        array(
                            'name' => 'Treatment Record',
                            'table_slug' => 'treatments',
                            'fields' => array('treatment_id', 'patient_id', 'appointment_id', 'treatment_date', 'diagnosis', 'treatment_description', 'medications', 'follow_up_required', 'follow_up_date', 'doctor_name', 'cost')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Active Patients',
                            'table_slug' => 'patients',
                            'columns' => array('patient_id', 'first_name', 'last_name', 'phone', 'insurance_provider', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Active')),
                            'sort_order' => 'last_name ASC'
                        ),
                        array(
                            'name' => 'Today\'s Appointments',
                            'table_slug' => 'appointments',
                            'columns' => array('appointment_id', 'patient_id', 'appointment_time', 'doctor_name', 'appointment_type', 'status'),
                            'filters' => array(array('field' => 'appointment_date', 'operator' => '=', 'value' => 'CURDATE()')),
                            'sort_order' => 'appointment_time ASC'
                        )
                    )
                )),
                'is_system' => 1
            ),

            // Property Management
            array(
                'template_key' => 'property_management',
                'name' => __('Property Management System', 'db-app-builder'),
                'description' => __('Real estate management with properties, tenants, leases, and maintenance tracking.', 'db-app-builder'),
                'category' => 'real_estate',
                'subcategory' => 'property_management',
                'icon' => 'dashicons-admin-home',
                'template_config' => json_encode(array(
                    'tables' => array(
                        array(
                            'slug' => 'properties',
                            'label' => 'Properties',
                            'description' => 'Property inventory and details',
                            'fields' => array(
                                array('slug' => 'property_id', 'label' => 'Property ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'property_name', 'label' => 'Property Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'address', 'label' => 'Address', 'type' => 'textarea', 'required' => 1),
                                array('slug' => 'property_type', 'label' => 'Property Type', 'type' => 'select', 'required' => 1, 'options' => array('Apartment', 'House', 'Condo', 'Commercial', 'Office', 'Warehouse', 'Other')),
                                array('slug' => 'bedrooms', 'label' => 'Bedrooms', 'type' => 'number', 'required' => 0),
                                array('slug' => 'bathrooms', 'label' => 'Bathrooms', 'type' => 'number', 'required' => 0),
                                array('slug' => 'square_feet', 'label' => 'Square Feet', 'type' => 'number', 'required' => 0),
                                array('slug' => 'monthly_rent', 'label' => 'Monthly Rent', 'type' => 'number', 'required' => 1),
                                array('slug' => 'security_deposit', 'label' => 'Security Deposit', 'type' => 'number', 'required' => 1),
                                array('slug' => 'purchase_price', 'label' => 'Purchase Price', 'type' => 'number', 'required' => 0),
                                array('slug' => 'purchase_date', 'label' => 'Purchase Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'property_manager', 'label' => 'Property Manager', 'type' => 'text', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Available', 'Occupied', 'Under Maintenance', 'Under Renovation', 'For Sale'))
                            )
                        ),
                        array(
                            'slug' => 'tenants',
                            'label' => 'Tenants',
                            'description' => 'Tenant information and records',
                            'fields' => array(
                                array('slug' => 'tenant_id', 'label' => 'Tenant ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'first_name', 'label' => 'First Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'last_name', 'label' => 'Last Name', 'type' => 'text', 'required' => 1),
                                array('slug' => 'email', 'label' => 'Email', 'type' => 'email', 'required' => 1),
                                array('slug' => 'phone', 'label' => 'Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'emergency_contact', 'label' => 'Emergency Contact', 'type' => 'text', 'required' => 1),
                                array('slug' => 'emergency_phone', 'label' => 'Emergency Phone', 'type' => 'text', 'required' => 1),
                                array('slug' => 'employer', 'label' => 'Employer', 'type' => 'text', 'required' => 0),
                                array('slug' => 'monthly_income', 'label' => 'Monthly Income', 'type' => 'number', 'required' => 0),
                                array('slug' => 'credit_score', 'label' => 'Credit Score', 'type' => 'number', 'required' => 0),
                                array('slug' => 'move_in_date', 'label' => 'Move-in Date', 'type' => 'date', 'required' => 0),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Former', 'Applicant', 'Evicted'))
                            )
                        ),
                        array(
                            'slug' => 'leases',
                            'label' => 'Leases',
                            'description' => 'Lease agreements and terms',
                            'fields' => array(
                                array('slug' => 'lease_id', 'label' => 'Lease ID', 'type' => 'text', 'required' => 1),
                                array('slug' => 'property_id', 'label' => 'Property', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'tenant_id', 'label' => 'Tenant', 'type' => 'relationship', 'required' => 1),
                                array('slug' => 'lease_start_date', 'label' => 'Lease Start Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'lease_end_date', 'label' => 'Lease End Date', 'type' => 'date', 'required' => 1),
                                array('slug' => 'monthly_rent', 'label' => 'Monthly Rent', 'type' => 'number', 'required' => 1),
                                array('slug' => 'security_deposit', 'label' => 'Security Deposit', 'type' => 'number', 'required' => 1),
                                array('slug' => 'lease_terms', 'label' => 'Lease Terms', 'type' => 'textarea', 'required' => 0),
                                array('slug' => 'pet_policy', 'label' => 'Pet Policy', 'type' => 'select', 'required' => 1, 'options' => array('No Pets', 'Cats Only', 'Dogs Only', 'Cats and Dogs', 'All Pets Allowed')),
                                array('slug' => 'renewal_option', 'label' => 'Renewal Option', 'type' => 'select', 'required' => 1, 'options' => array('Yes', 'No', 'Month-to-Month')),
                                array('slug' => 'status', 'label' => 'Status', 'type' => 'select', 'required' => 1, 'options' => array('Active', 'Expired', 'Terminated', 'Pending'))
                            )
                        )
                    ),
                    'forms' => array(
                        array(
                            'name' => 'Add Property',
                            'table_slug' => 'properties',
                            'fields' => array('property_id', 'property_name', 'address', 'property_type', 'bedrooms', 'bathrooms', 'square_feet', 'monthly_rent', 'security_deposit', 'property_manager')
                        ),
                        array(
                            'name' => 'Tenant Application',
                            'table_slug' => 'tenants',
                            'fields' => array('tenant_id', 'first_name', 'last_name', 'email', 'phone', 'emergency_contact', 'emergency_phone', 'employer', 'monthly_income', 'credit_score')
                        ),
                        array(
                            'name' => 'Create Lease',
                            'table_slug' => 'leases',
                            'fields' => array('lease_id', 'property_id', 'tenant_id', 'lease_start_date', 'lease_end_date', 'monthly_rent', 'security_deposit', 'lease_terms', 'pet_policy', 'renewal_option')
                        )
                    ),
                    'views' => array(
                        array(
                            'name' => 'Available Properties',
                            'table_slug' => 'properties',
                            'columns' => array('property_id', 'property_name', 'property_type', 'bedrooms', 'bathrooms', 'monthly_rent', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Available')),
                            'sort_order' => 'monthly_rent ASC'
                        ),
                        array(
                            'name' => 'Active Leases',
                            'table_slug' => 'leases',
                            'columns' => array('lease_id', 'property_id', 'tenant_id', 'lease_start_date', 'lease_end_date', 'monthly_rent', 'status'),
                            'filters' => array(array('field' => 'status', 'operator' => '=', 'value' => 'Active')),
                            'sort_order' => 'lease_end_date ASC'
                        )
                    )
                )),
                'is_system' => 1
            )
        );

        foreach ($default_templates as $template) {
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $templates_table WHERE template_key = %s",
                $template['template_key']
            ));

            if (!$existing) {
                $wpdb->insert($templates_table, $template);
            }
        }
    }

    /**
     * Test AJAX connectivity for debugging
     */
    public static function test_ajax_connectivity() {
        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Template Test AJAX: Request received');
            error_log('DAB Template Test AJAX: POST data: ' . print_r($_POST, true));
        }

        // Check if nonce is provided
        if (!isset($_POST['nonce'])) {
            wp_send_json_error('No nonce provided');
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'dab_templates_nonce')) {
            wp_send_json_error('Nonce verification failed');
        }

        // Return success response
        wp_send_json_success(array(
            'message' => 'AJAX connectivity test successful!',
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'user_can_manage' => current_user_can('manage_options'),
            'ajax_url' => admin_url('admin-ajax.php'),
            'template_manager_loaded' => class_exists('DAB_Template_Manager')
        ));
    }
}

// Initialize the Template Manager
DAB_Template_Manager::init();
